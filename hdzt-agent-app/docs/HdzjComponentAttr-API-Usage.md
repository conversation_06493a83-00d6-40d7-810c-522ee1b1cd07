# HdzjComponentAttr API 使用说明

## 概述

本文档介绍如何使用 HdzjComponentAttr 相关的 API 来处理活动组件属性数据。

## 核心功能

### 1. 自动数据库选择

系统会根据活动ID自动选择对应的数据库：

- 活动ID格式：`YYYYMM + n + 00 + m`（例如：2025073001）
- 其中 `n` 为套数（1-7），对应不同的数据库：
  - 套数1 → `hdzk`
  - 套数2 → `hdzk2_west`
  - 套数3 → `hdzk3_south`
  - 套数4 → `hdzk4_north`
  - 套数5 → `hdzk5_middle`
  - 套数6 → `hdzk6_up`
  - 套数7 → `hdzk7_down`

### 2. README格式JSON处理

支持处理以下格式的JSON数据：

```json
{
  "actId|cmptId|cmptUseInx": {
    "属性名1": "属性值1",
    "属性名2": "属性值2",
    "属性名3": ["数组值1", "数组值2"],
    "属性名4": {
      "嵌套属性1": "嵌套值1",
      "嵌套属性2": "嵌套值2"
    }
  }
}
```

## API 接口

### 1. 初始化活动组件（从请求头获取）

**接口地址：** `POST /api/component/initActComponent`

**请求头：**
- `jsonContent`: README格式的JSON字符串

**响应示例：**
```json
{
  "success": true,
  "message": "成功初始化活动组件，插入 13 条记录",
  "data": 13
}
```

**curl 示例：**
```bash
curl -X POST "http://localhost:8080/api/component/initActComponent" \
  -H "Content-Type: application/json" \
  -H "jsonContent: {\"2025073001|1016|810\":{\"actId\":2025073001,\"cmptId\":1016,\"cmptUseInx\":810,\"busiId\":400,\"giftIds\":[\"20001\",\"20002\"],\"apps\":[\"yy\"],\"threshold\":50000}}"
```

### 2. 初始化活动组件（通过请求体）

**接口地址：** `POST /api/component/initActComponent/body`

**请求体：** README格式的JSON字符串

**响应示例：**
```json
{
  "success": true,
  "message": "成功初始化活动组件，插入 13 条记录",
  "data": 13
}
```

**curl 示例：**
```bash
curl -X POST "http://localhost:8080/api/component/initActComponent/body" \
  -H "Content-Type: application/json" \
  -d '{
    "2025073001|1016|810": {
      "actId": 2025073001,
      "cmptId": 1016,
      "cmptUseInx": 810,
      "busiId": 400,
      "giftIds": ["20001", "20002"],
      "apps": ["yy"],
      "giftIcons": {
        "20001": "gift_icon_20001",
        "20002": "gift_icon_20002"
      },
      "threshold": 50000
    }
  }'
```

### 3. 根据活动ID删除组件属性

**接口地址：** `DELETE /api/component/deleteByActId/{actId}`

**路径参数：**
- `actId`: 活动ID

**响应示例：**
```json
{
  "success": true,
  "message": "成功删除 5 条记录",
  "data": 5
}
```

**curl 示例：**
```bash
curl -X DELETE "http://localhost:8080/api/component/deleteByActId/2025073001"
```

## 数据处理流程

### 1. JSON解析流程

1. 接收README格式的JSON数据
2. 解析JSON中的每个键值对
3. 从键中提取 `actId|cmptId|cmptUseInx` 信息
4. 根据活动ID自动选择对应的数据库
5. 将JSON对象中的每个属性转换为数据库记录
6. 按数据库分组批量插入

### 2. 数据转换规则

- **简单值**：直接转换为字符串
- **数组**：转换为JSON字符串格式
- **对象**：转换为JSON字符串格式
- **数字**：转换为字符串
- **布尔值**：转换为字符串（"true"/"false"）

### 3. 备注生成规则

系统会根据属性名自动生成有意义的备注：

- `actId` → "活动标识"
- `cmptId` → "组件标识"
- `cmptUseInx` → "组件使用序号"
- `busiId` → "业务标识"
- `giftIds` → "礼物ID列表"
- `apps` → "应用列表"
- `giftIcons` → "礼物图标映射"
- `tAwardPkgInfos` → "奖励包信息"
- `threshold` → "阈值配置"
- 其他 → "组件属性: {属性名}"

## 错误处理

### 常见错误及解决方案

1. **JSON格式错误**
   - 错误信息：`处理README格式JSON数据失败: Unexpected character...`
   - 解决方案：检查JSON格式是否正确

2. **活动ID格式错误**
   - 错误信息：`跳过无效的键格式: xxx`
   - 解决方案：确保键格式为 `actId|cmptId|cmptUseInx`

3. **数据库连接错误**
   - 错误信息：`插入数据库 xxx 失败`
   - 解决方案：检查数据库连接配置

4. **请求头缺失**
   - 错误信息：`请求头中缺少jsonContent字段或内容为空`
   - 解决方案：确保请求头包含 `jsonContent` 字段

## 注意事项

1. **活动ID格式**：必须是10位数字，格式为 `YYYYMM + n + 00 + m`
2. **套数范围**：套数必须在1-7之间，超出范围将使用默认数据库 `hdzk`
3. **JSON大小限制**：建议单次处理的JSON大小不超过1MB
4. **事务处理**：所有操作都在事务中执行，出错时会自动回滚
5. **数据库字段**：`hdzkdb` 字段仅用于内部逻辑，不会插入到数据库中

## 示例数据

### 完整的JSON示例

```json
{
  "2025073001|1016|810": {
    "actId": 2025073001,
    "cmptId": 1016,
    "cmptUseInx": 810,
    "busiId": 400,
    "giftIds": ["20001", "20002"],
    "apps": ["yy"],
    "giftIcons": {
      "20001": "gift_icon_20001",
      "20002": "gift_icon_20002"
    },
    "tAwardPkgInfos": {
      "2001": {
        "medalPic": "medal_primary_pic_2",
        "medalName": "新手勋章",
        "qrCode": "qr_code_primary_2"
      },
      "2002": {
        "medalPic": "medal_senior_pic_2",
        "medalName": "进阶勋章",
        "qrCode": "qr_code_senior_2"
      }
    },
    "tAwardTskMedalId": 2002,
    "tAwardPkgPrimaryId": 2001,
    "tAwardPkgSeniorId": 2002,
    "tAwardPkgExpertId": 0,
    "expertRankId": 0,
    "expertPhaseId": 0,
    "threshold": 50000
  }
}
```

这个JSON会被转换为13条数据库记录，插入到 `hdzk3_south` 数据库中。
