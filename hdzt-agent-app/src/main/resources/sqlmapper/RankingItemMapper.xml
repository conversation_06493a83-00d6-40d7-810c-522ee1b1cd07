<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yy.hdzt.mapper.hdzt.RankingItemMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.yy.hdzt.bean.hdzt.RankingItem">
        <id column="act_id" property="actId" jdbcType="INTEGER"/>
        <id column="rank_id" property="rankId" jdbcType="INTEGER"/>
        <result column="item_ids" property="itemIds" jdbcType="VARCHAR"/>
        <result column="item_alias" property="itemAlias" jdbcType="VARCHAR"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="extjson" property="extjson" jdbcType="VARCHAR"/>
        <result column="ctime" property="ctime" jdbcType="TIMESTAMP"/>
        <result column="utime" property="utime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        act_id, rank_id, item_ids, item_alias, remark, extjson, ctime, utime
    </sql>

</mapper>
