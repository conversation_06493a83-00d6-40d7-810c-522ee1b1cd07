<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yy.hdzt.mapper.hdzt.RankingPhaseGroupMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.yy.hdzt.bean.hdzt.RankingPhaseGroup">
        <id column="act_id" property="actId" jdbcType="INTEGER"/>
        <id column="phase_group_code" property="phaseGroupCode" jdbcType="VARCHAR"/>
        <result column="phase_group_name" property="phaseGroupName" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="TINYINT"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="extjson" property="extjson" jdbcType="VARCHAR"/>
        <result column="ctime" property="ctime" jdbcType="TIMESTAMP"/>
        <result column="utime" property="utime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        act_id, phase_group_code, phase_group_name, status, remark, extjson, ctime, utime
    </sql>

</mapper>
