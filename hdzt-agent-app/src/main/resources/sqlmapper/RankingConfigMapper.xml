<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yy.hdzt.mapper.hdzt.RankingConfigMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.yy.hdzt.bean.hdzt.RankingConfig">
        <id column="act_id" property="actId" jdbcType="INTEGER"/>
        <id column="rank_id" property="rankId" jdbcType="INTEGER"/>
        <result column="rank_name" property="rankName" jdbcType="VARCHAR"/>
        <result column="rank_name_show" property="rankNameShow" jdbcType="VARCHAR"/>
        <result column="rank_type" property="rankType" jdbcType="INTEGER"/>
        <result column="status" property="status" jdbcType="TINYINT"/>
        <result column="calc_begin_time" property="calcBeginTime" jdbcType="TIMESTAMP"/>
        <result column="calc_end_time" property="calcEndTime" jdbcType="TIMESTAMP"/>
        <result column="value_type" property="valueType" jdbcType="INTEGER"/>
        <result column="limit_item" property="limitItem" jdbcType="INTEGER"/>
        <result column="time_key" property="timeKey" jdbcType="TINYINT"/>
        <result column="time_key_begin" property="timeKeyBegin" jdbcType="VARCHAR"/>
        <result column="time_key_end" property="timeKeyEnd" jdbcType="VARCHAR"/>
        <result column="item_key" property="itemKey" jdbcType="TINYINT"/>
        <result column="member_key" property="memberKey" jdbcType="VARCHAR"/>
        <result column="phase_group_code" property="phaseGroupCode" jdbcType="VARCHAR"/>
        <result column="skip_rank_update" property="skipRankUpdate" jdbcType="INTEGER"/>
        <result column="skip_phase_update" property="skipPhaseUpdate" jdbcType="INTEGER"/>
        <result column="act_group" property="actGroup" jdbcType="INTEGER"/>
        <result column="is_show" property="isShow" jdbcType="TINYINT"/>
        <result column="show_begin_time" property="showBeginTime" jdbcType="TIMESTAMP"/>
        <result column="show_end_time" property="showEndTime" jdbcType="TIMESTAMP"/>
        <result column="position" property="position" jdbcType="INTEGER"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="extjson" property="extjson" jdbcType="VARCHAR"/>
        <result column="ctime" property="ctime" jdbcType="TIMESTAMP"/>
        <result column="utime" property="utime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        act_id, rank_id, rank_name, rank_name_show, rank_type, status, calc_begin_time, calc_end_time,
        value_type, limit_item, time_key, time_key_begin, time_key_end, item_key, member_key,
        phase_group_code, skip_rank_update, skip_phase_update, act_group, is_show, show_begin_time,
        show_end_time, position, remark, extjson, ctime, utime
    </sql>

</mapper>
