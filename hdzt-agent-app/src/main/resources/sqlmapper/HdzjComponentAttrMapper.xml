<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yy.hdzt.mapper.hdzk.HdzjComponentAttrMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.yy.hdzt.bean.hdzk.HdzjComponentAttr">
        <id column="act_id" property="actId" jdbcType="INTEGER"/>
        <id column="cmpt_id" property="cmptId" jdbcType="INTEGER"/>
        <id column="cmpt_use_inx" property="cmptUseInx" jdbcType="INTEGER"/>
        <id column="name" property="name" jdbcType="VARCHAR"/>
        <result column="value" property="value" jdbcType="VARCHAR"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="ctime" property="ctime" jdbcType="TIMESTAMP"/>
        <result column="utime" property="utime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        act_id, cmpt_id, cmpt_use_inx, name, value, remark, ctime, utime
    </sql>

</mapper>
