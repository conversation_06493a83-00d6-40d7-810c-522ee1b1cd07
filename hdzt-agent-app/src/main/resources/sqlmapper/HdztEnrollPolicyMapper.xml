<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yy.hdzt.mapper.hdzt.HdztEnrollPolicyMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.yy.hdzt.bean.hdzt.HdztEnrollPolicy">
        <id column="act_id" property="actId" jdbcType="INTEGER"/>
        <id column="role_id" property="roleId" jdbcType="INTEGER"/>
        <result column="check_flag" property="checkFlag" jdbcType="INTEGER"/>
        <result column="convert_flag" property="convertFlag" jdbcType="INTEGER"/>
        <result column="pass_way" property="passWay" jdbcType="INTEGER"/>
        <result column="auto_enroll" property="autoEnroll" jdbcType="INTEGER"/>
        <result column="extjson" property="extjson" jdbcType="VARCHAR"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        act_id, role_id, check_flag, convert_flag, pass_way, auto_enroll, extjson, remark
    </sql>

</mapper>
