<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yy.hdzt.mapper.hdzt.RankingPhaseMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.yy.hdzt.bean.hdzt.RankingPhase">
        <id column="act_id" property="actId" jdbcType="INTEGER"/>
        <id column="phase_id" property="phaseId" jdbcType="INTEGER"/>
        <result column="phase_name" property="phaseName" jdbcType="VARCHAR"/>
        <result column="phase_name_show" property="phaseNameShow" jdbcType="VARCHAR"/>
        <result column="phase_group_code" property="phaseGroupCode" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="TINYINT"/>
        <result column="phase_bg_url" property="phaseBgUrl" jdbcType="VARCHAR"/>
        <result column="begin_time" property="beginTime" jdbcType="TIMESTAMP"/>
        <result column="end_time" property="endTime" jdbcType="TIMESTAMP"/>
        <result column="show_begin_time" property="showBeginTime" jdbcType="TIMESTAMP"/>
        <result column="show_end_time" property="showEndTime" jdbcType="TIMESTAMP"/>
        <result column="task_value_type" property="taskValueType" jdbcType="INTEGER"/>
        <result column="recycle_count" property="recycleCount" jdbcType="INTEGER"/>
        <result column="recycle_type" property="recycleType" jdbcType="INTEGER"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="extjson" property="extjson" jdbcType="VARCHAR"/>
        <result column="ctime" property="ctime" jdbcType="TIMESTAMP"/>
        <result column="utime" property="utime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        act_id, phase_id, phase_name, phase_name_show, phase_group_code, status, phase_bg_url,
        begin_time, end_time, show_begin_time, show_end_time, task_value_type, recycle_count,
        recycle_type, remark, extjson, ctime, utime
    </sql>

</mapper>
