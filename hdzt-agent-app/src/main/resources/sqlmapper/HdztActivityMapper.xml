<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yy.hdzt.mapper.hdzt.HdztActivityMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.yy.hdzt.bean.hdzt.HdztActivity">
        <id column="act_id" property="actId" jdbcType="INTEGER"/>
        <result column="busi_id" property="busiId" jdbcType="INTEGER"/>
        <result column="act_name" property="actName" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="TINYINT"/>
        <result column="begin_time" property="beginTime" jdbcType="TIMESTAMP"/>
        <result column="end_time" property="endTime" jdbcType="TIMESTAMP"/>
        <result column="begin_time_show" property="beginTimeShow" jdbcType="TIMESTAMP"/>
        <result column="end_time_show" property="endTimeShow" jdbcType="TIMESTAMP"/>
        <result column="act_type" property="actType" jdbcType="INTEGER"/>
        <result column="act_group" property="actGroup" jdbcType="INTEGER"/>
        <result column="act_group_archive" property="actGroupArchive" jdbcType="INTEGER"/>
        <result column="secret" property="secret" jdbcType="VARCHAR"/>
        <result column="act_bg_url" property="actBgUrl" jdbcType="VARCHAR"/>
        <result column="detail_url" property="detailUrl" jdbcType="VARCHAR"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="extjson" property="extjson" jdbcType="VARCHAR"/>
        <result column="ctime" property="ctime" jdbcType="TIMESTAMP"/>
        <result column="utime" property="utime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        act_id, busi_id, act_name, status, begin_time, end_time, begin_time_show, end_time_show,
        act_type, act_group, act_group_archive, secret, act_bg_url, detail_url, remark, extjson,
        ctime, utime
    </sql>

</mapper>
