spring:
  application:
    name: hdzt-agent
  main:
    web-application-type: servlet

  ai:
    mistralai:
      api-key: 1EdQGenxuj9wzsQrWAMyFmTGeZKpJjw4 # lwz
    openai:
      base-url: http://localhost:8008/
      api-key: uwiQYZHyrN1pGj0pYGs07TCeh3tLCvUqvbizzpf8Gz1g_BynogsOH1apRX0qcdzyjC_$ouuJS14Z0 # qwen lwz
      chat:
        options:
          model: free
    dashscope:
      api-key: sk-b93546dc61c241969db9edb220b250c0  # lwz
      chat:
        options:
          model: qwen-plus-2025-04-28
    mcp:
      server:
        name: hdzt-agent
        stdio: false
        type: sync
        sse-endpoint: /api/mcp/sse
        sse-message-endpoint: /api/mcp/message

  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
    druid:
      hdzt:
        username: udb_data@zwyz_mysql_test

logging:
  level:
    org.springframework.ai: debug

mybatis:
  mapper-locations: classpath:mapper/*.xml
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    map-underscore-to-camel-case: true
