package com.yy.hdzt.boot;

import com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceBuilder;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.sql.DataSource;

@Configuration
@EnableAutoConfiguration(exclude = DataSourceAutoConfiguration.class)
@MapperScan("com.yy.hdzt.mapper")
public class DataSourceConfig {

    @Bean("hdztDataSource")
    @ConfigurationProperties(prefix = "spring.datasource.druid.hdzt")
    public DataSource hdztDataSource() throws Exception {
        return DruidDataSourceBuilder.create().build();
    }

}
