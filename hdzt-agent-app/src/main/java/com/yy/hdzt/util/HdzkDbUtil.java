package com.yy.hdzt.util;

import lombok.extern.slf4j.Slf4j;

/**
 * HDZK数据库工具类
 * 根据活动ID的套数选择对应的数据库
 */
@Slf4j
public class HdzkDbUtil {

    /**
     * 根据活动ID获取对应的数据库名称
     * 活动ID格式：年月+n00m，其中n为套数
     * 例如：2025061002 -> 年月(202506) + 套数(1) + 00 + m(2)
     * 
     * @param actId 活动ID
     * @return 数据库名称
     */
    public static String getHdzkDbByActId(Long actId) {
        if (actId == null) {
            log.warn("活动ID为空，使用默认数据库hdzk");
            return "hdzk";
        }

        try {
            // 将活动ID转换为字符串
            String actIdStr = actId.toString();
            
            // 活动ID应该是10位数字
            if (actIdStr.length() != 10) {
                log.warn("活动ID格式不正确，长度应为10位: {}, 使用默认数据库hdzk", actId);
                return "hdzk";
            }
            
            // 提取套数：第7位数字
            // 格式：YYYYMM + n + 00 + m
            // 例如：2025061002 -> 202506(年月) + 1(套数) + 00 + 2
            char setNumChar = actIdStr.charAt(6);
            int setNum = Character.getNumericValue(setNumChar);
            
            return getDbNameBySetNum(setNum);
            
        } catch (Exception e) {
            log.error("解析活动ID失败: {}, 使用默认数据库hdzk", actId, e);
            return "hdzk";
        }
    }

    public static String getFirstDbName() {
        return "hdzk";
    }

    /**
     * 根据套数获取数据库名称
     * 
     * @param setNum 套数 (1-7)
     * @return 数据库名称
     */
    private static String getDbNameBySetNum(int setNum) {
        switch (setNum) {
            case 1:
                return "hdzk";
            case 2:
                return "hdzk2_west";
            case 3:
                return "hdzk3_south";
            case 4:
                return "hdzk4_north";
            case 5:
                return "hdzk5_middle";
            case 6:
                return "hdzk6_up";
            case 7:
                return "hdzk7_down";
            default:
                log.warn("套数超出范围 (1-7): {}, 使用默认数据库hdzk", setNum);
                return "hdzk";
        }
    }

    /**
     * 验证活动ID格式是否正确
     * 
     * @param actId 活动ID
     * @return 是否格式正确
     */
    public static boolean isValidActIdFormat(Integer actId) {
        if (actId == null) {
            return false;
        }

        String actIdStr = actId.toString();
        
        // 检查长度
        if (actIdStr.length() != 10) {
            return false;
        }
        
        // 检查是否全为数字
        if (!actIdStr.matches("\\d{10}")) {
            return false;
        }
        
        // 检查年份范围 (2020-2099)
        String yearStr = actIdStr.substring(0, 4);
        int year = Integer.parseInt(yearStr);
        if (year < 2020 || year > 2099) {
            return false;
        }
        
        // 检查月份范围 (01-12)
        String monthStr = actIdStr.substring(4, 6);
        int month = Integer.parseInt(monthStr);
        if (month < 1 || month > 12) {
            return false;
        }
        
        // 检查套数范围 (1-7)
        char setNumChar = actIdStr.charAt(6);
        int setNum = Character.getNumericValue(setNumChar);
        if (setNum < 1 || setNum > 7) {
            return false;
        }
        
        // 检查固定位是否为00
        String fixedPart = actIdStr.substring(7, 9);
        if (!"00".equals(fixedPart)) {
            return false;
        }
        
        return true;
    }

    /**
     * 从活动ID中提取套数
     * 
     * @param actId 活动ID
     * @return 套数，解析失败返回1
     */
    public static int extractSetNum(Integer actId) {
        if (actId == null) {
            return 1;
        }

        try {
            String actIdStr = actId.toString();
            if (actIdStr.length() >= 7) {
                char setNumChar = actIdStr.charAt(6);
                int setNum = Character.getNumericValue(setNumChar);
                if (setNum >= 1 && setNum <= 7) {
                    return setNum;
                }
            }
        } catch (Exception e) {
            log.warn("提取套数失败: {}", actId, e);
        }
        
        return 1; // 默认返回套数1
    }

    /**
     * 获取所有可用的数据库名称
     * 
     * @return 数据库名称数组
     */
    public static String[] getAllDbNames() {
        return new String[]{
            "hdzk",
            "hdzk2_west",
            "hdzk3_south",
            "hdzk4_north",
            "hdzk5_middle",
            "hdzk6_up",
            "hdzk7_down"
        };
    }
}
