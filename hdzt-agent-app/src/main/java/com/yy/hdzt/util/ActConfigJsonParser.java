package com.yy.hdzt.util;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.yy.hdzt.template.ActConfigTemplate;
import com.yy.java.component.util.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * ActConfigTemplate JSON 解析工具类
 */
@Component
@Slf4j
public class ActConfigJsonParser {
    
    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

    private static final Pattern JSON_PATTERN = Pattern.compile("```json\\s*\\n(.*?)\\n```", Pattern.DOTALL);

    private static final Pattern DIRECT_JSON_PATTERN = Pattern.compile("\\{.*\\}", Pattern.DOTALL);

    /**
     * 从复杂的 JSON 响应中提取 ActConfigTemplate
     * 
     * @param jsonText 完整的 JSON 响应文本
     * @return ActConfigTemplate 对象
     */
    public ActConfigTemplate parseFromChatResponse(String jsonText) {
        try {
            // 1. 解析外层 JSON
            JsonNode rootNode = OBJECT_MAPPER.readTree(jsonText);
            
            // 2. 提取 text 字段中的 JSON 内容
            String textContent = extractTextContent(rootNode);
            
            if (textContent == null) {
                throw new RuntimeException("无法从响应中提取 text 内容");
            }
            
            // 3. 从 text 内容中提取 JSON
            String actConfigJson = extractJsonFromText(textContent);
            
            if (actConfigJson == null) {
                throw new RuntimeException("无法从 text 内容中提取 JSON");
            }
            
            // 4. 解析为 ActConfigTemplate
            return JsonUtils.deserialize(actConfigJson, ActConfigTemplate.class);
            
        } catch (Exception e) {
            log.error("解析 ActConfigTemplate 失败: {}", e.getMessage(), e);
            throw new RuntimeException("解析 ActConfigTemplate 失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 从响应节点中提取 text 内容
     */
    private String extractTextContent(JsonNode rootNode) {
        // 尝试多种路径提取 text 内容
        String[] paths = {
            "/chatResponse/result/output/text",
            "/chatResponse/results/0/output/text",
            "/result/output/text",
            "/output/text",
            "/text"
        };
        
        for (String path : paths) {
            JsonNode textNode = rootNode.at(path);
            if (textNode != null && !textNode.isMissingNode() && textNode.isTextual()) {
                return textNode.asText();
            }
        }
        
        return null;
    }
    
    /**
     * 从 text 内容中提取 JSON（处理 markdown 格式）
     */
    private String extractJsonFromText(String textContent) {
        // 处理转义字符
        String unescapedText = textContent.replace("\\n", "\n")
                                         .replace("\\\"", "\"")
                                         .replace("\\\\", "\\");
        
        // 使用正则表达式提取 JSON 内容
        Matcher matcher = JSON_PATTERN.matcher(unescapedText);
        
        if (matcher.find()) {
            return matcher.group(1).trim();
        }
        
        // 如果没有 markdown 格式，尝试直接提取 JSON
        Matcher directMatcher = DIRECT_JSON_PATTERN.matcher(unescapedText);
        
        if (directMatcher.find()) {
            return directMatcher.group().trim();
        }
        
        return null;
    }
    
    /**
     * 验证 ActConfigTemplate 是否有效
     */
    public boolean validateActConfigTemplate(ActConfigTemplate template) {
        if (template == null) {
            return false;
        }
        
        // 基本字段验证
        if (template.getActId() == null || template.getActId().trim().isEmpty()) {
            log.warn("actId 为空");
            return false;
        }
        
        if (template.getActName() == null || template.getActName().trim().isEmpty()) {
            log.warn("actName 为空");
            return false;
        }
        
        if (template.getBeginTime() == null || template.getBeginTime().trim().isEmpty()) {
            log.warn("beginTime 为空");
            return false;
        }
        
        if (template.getEndTime() == null || template.getEndTime().trim().isEmpty()) {
            log.warn("endTime 为空");
            return false;
        }
        return true;
    }
}
