package com.yy.hdzt.handler;

import com.yy.hdzt.service.ChatClientHolder;
import com.yy.hdzt.template.ActConfigTemplate;
import com.yy.hdzt.vo.ActChatReq;
import com.yy.java.component.util.JsonUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.memory.ChatMemory;
import org.springframework.ai.chat.prompt.PromptTemplate;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> 2025/7/17
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ActConfigHandler {

    private final ChatClientHolder chatClientHolder;

    public static final String ACT_CONFIG_PROMPT = """
            你是一个活动配置专家, 你的目标是根据用户的需求构建或优化活动配置.
            
            活动配置为一份JSON, 主要包含以下内容:
            1. 活动信息
            2. 角色信息
            3. 礼物信息
            4. 赛程信息
            5. 赛程阶段信息
            6. 排行榜信息
            
            通常我们会先确定活动的角色信息, 然后确定活动的礼物信息, 再确定活动的赛程信息和赛程阶段信息, 最后确定活动的排行榜信息
            
            一些通用业务知识
            1. 语音房/聊天室/技能卡 是同义词
            2. 活动系统一共有7套, 默认使用第6套
            
            请严格按照活动配置的JSON结构, 结合用户需求构建活动配置JSON, 不要生成说明里没有的字段, 只需要返回JSON
            """;

    public static final PromptTemplate QUESTION_TEMPLATE = PromptTemplate.builder().template("""
            需求文档:{document}
            活动配置JSON说明:{actConfig}
            """).build();

    public String chat(ActChatReq req, Map<String, Object> context) {

        String sessionId = req.getSessionId();

        // 构造工具上下文
        Map<String, Object> toolContext = new HashMap<>();
        toolContext.put(ChatMemory.CONVERSATION_ID, sessionId);

        String response = chatClientHolder.getChatClient(req.getModel()).prompt()
                .system(s -> s.text(ACT_CONFIG_PROMPT))
                .user(QUESTION_TEMPLATE.create(Map.of("document", req.getDocument(), "actConfig", JsonUtils.serialize(new ActConfigTemplate()))).getContents())
                .advisors(advisorSpec -> advisorSpec.param(ChatMemory.CONVERSATION_ID, sessionId))
                .toolContext(toolContext)
                .call()
                .content();

        return response;
    }

}
