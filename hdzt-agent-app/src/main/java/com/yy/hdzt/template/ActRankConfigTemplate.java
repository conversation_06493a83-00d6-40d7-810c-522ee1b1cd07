package com.yy.hdzt.template;

import lombok.Data;

/**
 * ranking_config
 *
 * <AUTHOR> 2025/6/17
 */
@Data
public class ActRankConfigTemplate {

    private String rankId = """
            赛道标识, 赛程id*100之后递增, 赛道下按阶段分别累榜, 一对一晋级关系的为同一个赛道, 赛道和阶段用于交织生成榜单
            例如: 主持赛程, 定级赛道N进100, 前40名进入卓越赛道, 后60名进入精英赛道, 然后卓越和精英赛道分别进行晋级赛/PK赛/决赛阶段, 晋级赛/PK赛/决赛阶段是一对一晋级关系, 可以视为同一赛道
            那么主持赛程可分为三个赛道, 定级赛道, 卓越赛道, 精英赛道, 其他的以此类推
            """;

    private String rankName = "赛道名称";

    private String beginTime = "赛道开始时间 格式为yyyy-MM-dd HH:mm:ss";

    private String endTime = "赛道结束时间 格式为yyyy-MM-dd HH:mm:ss 如果是24:00:00 要写成昨日23:59:59";

    private String timeKey = "按时间分榜, 需要动态按时间段分榜时使用, 0-不分 1-按日分 2-按小时分 3-按周分 4-按月分 5-按季度分 6-按年分 7-按15分钟分 8-按30分钟分 9-按5分钟分 10-按10分钟分";

    private String itemKey = "按礼物分榜, 需要动态按礼物分榜时使用, 0-不分 1-按礼物分";

    private String memberKey = "按角色分榜, 角色用户id作用于zset的key, 用于贡献榜, 累计累榜角色用户对该角色用户的贡献";

    private String roles = "累榜角色, 角色用户id作为zset的member, 如果有多个用|分割, 如果为角色组合(CP)用&连接";

    private String itemIds = "累榜礼物编码, 用逗号分开";

}
