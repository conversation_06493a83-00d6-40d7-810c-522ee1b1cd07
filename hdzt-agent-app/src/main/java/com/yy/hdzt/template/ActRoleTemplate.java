package com.yy.hdzt.template;

import lombok.Data;

/**
 * hdzt_enroll_policy
 *
 * <AUTHOR> 2025/7/15
 */
@Data
public class ActRoleTemplate {

    private String roleId = """
            角色Id
            50001,交友主持
            50003,交友用户
            50004,交友公会
            50005,礼物
            50011,女视频
            50012,女声音
            50013,交友男主持
            50014,天团
            50015,交友女主持
            50016,新锐
            50017,男视频
            50018,男声音
            50019,交友多人超主
            50020,交友帽子主持
            50021,交友视频主持
            50022,交友声音主持
            50023,交友黑马天团
            50033,交友PK用户
            50041,相亲
            50042,多人
            50043,交友公会1
            50044,交友公会2
            50050,交友厅
            50051,交友-多人厅
            50052,交友-天团厅
            50053,交友-家族厅
            50054,交友-相亲厅
            50055,交友综合厅
            50056,交友黑马厅
            50057,交友厅2
            50058,交友厅3
            50059,交友厅4
            50060,厅ID
            50070,厅管
            50071,厅管B
            51003,交友送礼用户
            51004,交友手Y用户
            81001,追玩技能卡主播
            81003,追玩技能卡送礼用户
            81004,追玩技能卡公会
            81005,充能条
            81007,技能卡用户2
            81011,追玩技能卡主播-A
            81012,追玩技能卡主播-B
            81013,追玩技能卡主播-C
            81014,追玩技能卡主播-D
            81050,技能卡经营厅
            81051,技能卡房间
            81070,语音房家族
            81071,语音房王者家族
            81072,语音房星锐家族
            81073,语音房黑马家族
            5002001,闪亮分会场天团
            5002002,闪亮分赛场交友女视频
            5002003,闪亮分赛场交友男视频
            5002004,闪亮分赛场交友男音频
            5002005,闪亮分赛场交友女音频
            5002006,交友新锐男视频
            5002007,交友新锐女视频
            5002008,交友新锐男音频
            5002009,交友新锐女音频
            5002010,交友乱斗主持
            5004001,交友新锐公会根据文档角色选择 810业务的用语音房或者技能卡角色 500业务用50开头角色
            """;

    private String remark = "角色备注";

    private String autoEnroll = "0-不自动报名 1-自动报名 主播主持房间号公会需要报名";

    private String checkFlag = "报名检查 0-不检查 1-要检查 默认0";

    private String passWay = "通过方式，当没有报名时：0-不通过，1-通过 1";

    private String convertFlag = "转换标志，当有报名记录时：0-不转成dest role， 1-转成dest role 默认0";

}
