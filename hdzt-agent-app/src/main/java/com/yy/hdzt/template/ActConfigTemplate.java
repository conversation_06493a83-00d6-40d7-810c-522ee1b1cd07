package com.yy.hdzt.template;

import lombok.Data;

import java.util.List;

/**
 * hdzt_activity
 *
 * <AUTHOR> 2025/6/15
 */
@Data
public class ActConfigTemplate {

    private String busiId = "业务id，语音房/聊天室是810，交友是500";

    private String actId = "活动id, 由年月+n00m组成, 如202508n00m, n是活动套数, m是当月第几个活动";

    private String actGroup = "使用活动id的n作为套数";

    private String hdztDb = """
            1-7套 根据套数选择, 分别是
            hdzt, hdzt2_west, hdzt3_south, hdzt4_north, hdzt5_middle, hdzt6_up, hdzt7_down
            """;

    private String hdzkDb = """
            1-7套 根据套数选择, 分别是
            hdzk, hdzk2_west, hdzk3_south, hdzk4_north, hdzk5_middle, hdzk6_up, hdzk7_down
            """;

    private String actName = "活动名称";

    private String beginTime = "活动开始时间, 格式为yyyy-MM-dd HH:mm:ss";

    private String endTime = "活动结束时间, 格式为yyyy-MM-dd HH:mm:ss 如果是24:00:00 要写成昨日23:59:59";

    // 角色
    private List<ActRoleTemplate> actRoles = List.of(new ActRoleTemplate());

    // 礼物
    private List<ActGiftTemplate> actGifts = List.of(new ActGiftTemplate());

    // 赛程
    private List<ActScheduleTemplate> actSchedules = List.of(new ActScheduleTemplate());

    // 奖池


}
