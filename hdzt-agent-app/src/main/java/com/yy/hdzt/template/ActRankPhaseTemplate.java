package com.yy.hdzt.template;

import lombok.Data;

/**
 * ranking_phase
 *
 * <AUTHOR> 2025/7/15
 */
@Data
public class ActRankPhaseTemplate {

    private String phaseId = "阶段id, 赛程id*10后递增, 赛程有一到多个阶段, 时间段相同聚合为同一阶段, 作用于赛程下所有赛道";

    private String phaseName = "阶段名称";

    private String beginTime = "开始时间 格式为yyyy-MM-dd HH:mm:ss";

    private String endTime = "结束时间 格式为yyyy-MM-dd HH:mm:ss 如果是24:00:00 要写成昨日23:59:59";

}
