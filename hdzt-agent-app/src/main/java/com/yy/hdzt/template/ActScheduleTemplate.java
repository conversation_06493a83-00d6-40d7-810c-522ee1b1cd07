package com.yy.hdzt.template;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> 2025/7/15
 */
@Data
public class ActScheduleTemplate {

    private String id = "赛程id, 从1开始, 赛程下由一到多个阶段, 所有阶段时间完全相同的可合并为同一赛程";

    private String scheduleName = "赛程名称, 例如主持赛程, 强厅赛程, 活动全程等";

    private String scheduleCode = "赛程编码, 英文";

    private List<ActRankPhaseTemplate> actRankPhases = List.of(new ActRankPhaseTemplate());

    private List<ActRankConfigTemplate> actRankConfigs = List.of(new ActRankConfigTemplate());

    private List<ActRankPhaseQualificationTemplate> actRankPhaseQualifications = List.of(new ActRankPhaseQualificationTemplate());

}
