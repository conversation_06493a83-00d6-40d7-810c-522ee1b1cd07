package com.yy.hdzt.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.yy.hdzt.bean.hdzk.HdzjComponent;
import com.yy.hdzt.bean.hdzk.HdzjComponentAttr;
import com.yy.hdzt.bean.hdzk.HdzjComponentDefine;
import com.yy.hdzt.consts.Hdzk;
import com.yy.hdzt.mapper.hdzk.HdzjComponentAttrMapper;
import com.yy.hdzt.mapper.hdzk.HdzjComponentDefineMapper;
import com.yy.hdzt.mapper.hdzk.HdzjComponentMapper;
import com.yy.hdzt.util.HdzkDbUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 组件属性服务类
 * 处理README格式的JSON数据并插入到hdzj_component_attr表
 *
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class HdzjComponentAttrService {

    private final HdzjComponentAttrMapper hdzjComponentAttrMapper;
    private final ObjectMapper objectMapper = new ObjectMapper();
    private final HdzjComponentDefineMapper hdzjComponentDefineMapper;
    private final HdzjComponentMapper hdzjComponentMapper;

    /**
     * 初始化活动组件
     * 从请求头获取jsonContent并处理README格式的JSON数据
     *
     * @param jsonContent README格式的JSON字符串
     * @return 成功插入的记录数
     */
    @Transactional(rollbackFor = Exception.class)
    public int initActComponent(String jsonContent) {
        try {
            log.info("开始初始化活动组件，JSON内容长度: {}", jsonContent != null ? jsonContent.length() : 0);

            if (jsonContent == null || jsonContent.trim().isEmpty()) {
                log.warn("JSON内容为空，无法初始化活动组件");
                return 0;
            }

            // 调用处理README格式JSON的方法
            int insertCount = processReadmeJsonAndInsert(jsonContent);

            log.info("活动组件初始化完成，共插入 {} 条记录", insertCount);
            return insertCount;

        } catch (Exception e) {
            log.error("初始化活动组件失败: {}", e.getMessage(), e);
            throw new RuntimeException("初始化活动组件失败: " + e.getMessage(), e);
        }
    }

    /**
     * 处理README格式的JSON并插入到hdzj_component_attr表
     * <p>
     * JSON格式示例:
     * {
     * "actId|cmptId|cmptUseInx": {
     * "属性名1": "属性值1",
     * "属性名2": "属性值2",
     * ...
     * }
     * }
     *
     * @param jsonContent README格式的JSON字符串
     * @return 成功插入的记录数
     */
    @Transactional(rollbackFor = Exception.class)
    public int processReadmeJsonAndInsert(String jsonContent) {
        try {
            log.info("开始处理README格式JSON数据");

            // 解析JSON
            JsonNode rootNode = objectMapper.readTree(jsonContent);

            // 转换为组件属性列表
            List<HdzjComponentAttr> componentAttrs = parseJsonToComponentAttrs(rootNode);

            if (componentAttrs.isEmpty()) {
                log.warn("没有解析到有效的组件属性数据");
                return 0;
            }
            List<HdzjComponent> hdzjComponent = parseJsonToComponent(rootNode);

            // 按数据库分组并批量插入
            int res = batchInsertByDatabase(componentAttrs);
            res += batchInsertHdzjComponentByDatabase(hdzjComponent);
            return res;

        } catch (Exception e) {
            log.error("处理README格式JSON数据失败: {}", e.getMessage(), e);
            throw new RuntimeException("处理README格式JSON数据失败: " + e.getMessage(), e);
        }
    }

    /**
     * 按数据库分组并批量插入组件属性
     *
     * @param componentAttrs 组件属性列表
     * @return 总插入记录数
     */
    private int batchInsertByDatabase(List<HdzjComponentAttr> componentAttrs) {
        // 按数据库名称分组
        Map<String, List<HdzjComponentAttr>> dbGroupMap = componentAttrs.stream()
                .collect(java.util.stream.Collectors.groupingBy(HdzjComponentAttr::getHdzkdb));

        int totalInsertCount = 0;

        // 分别插入到不同数据库
        for (Map.Entry<String, List<HdzjComponentAttr>> entry : dbGroupMap.entrySet()) {
            String dbName = entry.getKey();
            List<HdzjComponentAttr> attrs = entry.getValue();

            try {
                int insertCount = hdzjComponentAttrMapper.batchInsertAttrs(dbName, attrs);
                totalInsertCount += insertCount;


                log.info("成功插入 {} 条组件属性记录到数据库 {}", insertCount, dbName);
            } catch (Exception e) {
                log.error("插入数据库 {} 失败: {}", dbName, e.getMessage(), e);
                throw new RuntimeException("插入数据库 " + dbName + " 失败: " + e.getMessage(), e);
            }
        }

        log.info("总共插入 {} 条组件属性记录", totalInsertCount);
        return totalInsertCount;
    }

    /**
     * 按数据库分组并批量插入组件属性
     *
     * @param hdzjComponent 组件列表
     * @return 总插入记录数
     */
    private int batchInsertHdzjComponentByDatabase(List<HdzjComponent> hdzjComponent) {
        // 按数据库名称分组
        Map<String, List<HdzjComponent>> dbGroupMap = hdzjComponent.stream()
                .collect(java.util.stream.Collectors.groupingBy(HdzjComponent::getHdzkdb));

        int totalInsertCount = 0;

        // 分别插入到不同数据库
        for (Map.Entry<String, List<HdzjComponent>> entry : dbGroupMap.entrySet()) {
            String dbName = entry.getKey();
            List<HdzjComponent> components = entry.getValue();

            try {
                int insertCount = hdzjComponentMapper.batchInsertHdzjComponent(dbName, components);
                totalInsertCount += insertCount;


                log.info("成功插入 {} 条组件记录到数据库 {}", insertCount, dbName);
            } catch (Exception e) {
                log.error("插入数据库 {} 失败: {}", dbName, e.getMessage(), e);
                throw new RuntimeException("插入数据库 " + dbName + " 失败: " + e.getMessage(), e);
            }
        }

        log.info("总共插入 {} 条组件属性记录", totalInsertCount);
        return totalInsertCount;
    }

    /**
     * 解析JSON节点为组件属性列表
     *
     * @param rootNode JSON根节点
     * @return 组件属性列表
     */
    private List<HdzjComponentAttr> parseJsonToComponentAttrs(JsonNode rootNode) {
        List<HdzjComponentAttr> componentAttrs = new ArrayList<>();

        // 遍历JSON的每个键值对
        Iterator<Map.Entry<String, JsonNode>> fields = rootNode.fields();
        while (fields.hasNext()) {
            Map.Entry<String, JsonNode> entry = fields.next();
            String key = entry.getKey();
            JsonNode valueNode = entry.getValue();

            // 解析键格式: "actId|cmptId|cmptUseInx"
            ComponentKey componentKey = parseComponentKey(key);
            if (componentKey == null) {
                log.warn("跳过无效的键格式: {}", key);
                continue;
            }

            // 解析属性值
            List<HdzjComponentAttr> attrs = parseComponentAttributes(componentKey, valueNode);
            componentAttrs.addAll(attrs);
        }

        return componentAttrs;
    }

    /**
     * 解析JSON节点为组件属性列表
     *
     * @param rootNode JSON根节点
     * @return 组件属性列表
     */
    private List<HdzjComponent> parseJsonToComponent(JsonNode rootNode) {
        List<HdzjComponent> hdzjComponents = Lists.newArrayList();

        List<HdzjComponentDefine> hdzjComponentDefines = hdzjComponentDefineMapper.selectComponentsByState(HdzkDbUtil.getFirstDbName(), Hdzk.HdzkCompoentDefineStatus.STATE_NORMAL);
        Map<Long, HdzjComponentDefine> hdzjComponentDefineMap = hdzjComponentDefines.stream().collect(Collectors.toMap(HdzjComponentDefine::getCmptId, p -> p));
        // 遍历JSON的每个键值对
        Iterator<Map.Entry<String, JsonNode>> fields = rootNode.fields();
        while (fields.hasNext()) {
            Map.Entry<String, JsonNode> entry = fields.next();
            String key = entry.getKey();
            JsonNode valueNode = entry.getValue();

            // 解析键格式: "actId|cmptId|cmptUseInx"
            ComponentKey componentKey = parseComponentKey(key);
            if (componentKey == null) {
                log.warn("跳过无效的键格式: {}", key);
                continue;
            }

            // 解析属性值
            HdzjComponent hdzjComponent = parseComponent(hdzjComponentDefineMap, componentKey, valueNode);
            hdzjComponents.add(hdzjComponent);
        }

        return hdzjComponents;
    }

    /**
     * 解析组件键
     *
     * @param key 格式为 "actId|cmptId|cmptUseInx" 的字符串
     * @return 解析后的组件键对象，解析失败返回null
     */
    private ComponentKey parseComponentKey(String key) {
        try {
            String[] parts = key.split("\\|");
            if (parts.length != 3) {
                return null;
            }

            Long actId = Long.parseLong(parts[0].trim());
            Long cmptId = Long.parseLong(parts[1].trim());
            Long cmptUseInx = Long.parseLong(parts[2].trim());

            return new ComponentKey(actId, cmptId, cmptUseInx);

        } catch (NumberFormatException e) {
            log.warn("解析组件键失败: {}, 错误: {}", key, e.getMessage());
            return null;
        }
    }

    /**
     * 解析组件属性
     *
     * @param componentKey 组件键
     * @param valueNode    属性值节点
     * @return 组件属性列表
     */
    private HdzjComponent parseComponent(Map<Long, HdzjComponentDefine> hdzjComponentDefineMap, ComponentKey componentKey, JsonNode valueNode) {
        HdzjComponent hdzjComponent = new HdzjComponent();
        HdzjComponentDefine hdzjComponentDefine = hdzjComponentDefineMap.get(componentKey.getCmptId());
        if (hdzjComponentDefine == null) {
            throw new RuntimeException("组件定义不存在: " + componentKey.getCmptId());
        }

        hdzjComponent.setActId(componentKey.getActId());
        hdzjComponent.setCmptId(hdzjComponentDefine.getCmptId());
        hdzjComponent.setCmptUseInx(componentKey.getCmptUseInx());
        hdzjComponent.setCmptTitle(hdzjComponentDefine.getCmptTitle());
        hdzjComponent.setModuleName(valueNode.get("moduleName").asText());
        hdzjComponent.setStatus(Hdzk.HdzkCompoentDefineStatus.STATE_NORMAL);
        hdzjComponent.setCtime(new Date());
        hdzjComponent.setUtime(new Date());
        hdzjComponent.setShowOrder(0);
        // 根据活动ID设置数据库名称
        hdzjComponent.setHdzkdb(HdzkDbUtil.getHdzkDbByActId(componentKey.getActId()));
        return hdzjComponent;
    }

    /**
     * 解析组件属性
     *
     * @param componentKey 组件键
     * @param valueNode    属性值节点
     * @return 组件属性列表
     */
    private List<HdzjComponentAttr> parseComponentAttributes(ComponentKey componentKey, JsonNode valueNode) {
        List<HdzjComponentAttr> attrs = new ArrayList<>();

        if (!valueNode.isObject()) {
            log.warn("属性值不是对象类型，跳过处理");
            return attrs;
        }

        // 遍历属性对象的每个字段
        Iterator<Map.Entry<String, JsonNode>> fields = valueNode.fields();
        while (fields.hasNext()) {
            Map.Entry<String, JsonNode> field = fields.next();
            String attrName = field.getKey();
            JsonNode attrValueNode = field.getValue();

            // 创建组件属性对象
            HdzjComponentAttr attr = new HdzjComponentAttr();
            attr.setActId(componentKey.getActId());
            attr.setCmptId(componentKey.getCmptId());
            attr.setCmptUseInx(componentKey.getCmptUseInx());
            attr.setName(attrName);
            attr.setValue(convertNodeToString(attrValueNode));
            attr.setRemark(generateRemark(attrName));
            // 根据活动ID设置数据库名称
            attr.setHdzkdb(HdzkDbUtil.getHdzkDbByActId(componentKey.getActId()));

            attrs.add(attr);
        }

        return attrs;
    }

    /**
     * 将JsonNode转换为字符串
     *
     * @param node JSON节点
     * @return 字符串值
     */
    private String convertNodeToString(JsonNode node) {
        if (node.isTextual()) {
            return node.asText();
        } else if (node.isNumber()) {
            return node.asText();
        } else if (node.isBoolean()) {
            return String.valueOf(node.asBoolean());
        } else if (node.isArray() || node.isObject()) {
            // 对于数组和对象，转换为JSON字符串
            try {
                return objectMapper.writeValueAsString(node);
            } catch (Exception e) {
                log.warn("转换复杂对象为字符串失败: {}", e.getMessage());
                return node.toString();
            }
        } else {
            return node.asText();
        }
    }

    /**
     * 根据属性名生成备注
     *
     * @param attrName 属性名
     * @return 备注信息
     */
    private String generateRemark(String attrName) {
        // 可以根据属性名生成更有意义的备注
        switch (attrName) {
            case "actId":
                return "活动标识";
            case "cmptId":
                return "组件标识";
            case "cmptUseInx":
                return "组件使用序号";
            case "busiId":
                return "业务标识";
            case "giftIds":
                return "礼物ID列表";
            case "apps":
                return "应用列表";
            case "giftIcons":
                return "礼物图标映射";
            case "tAwardPkgInfos":
                return "奖励包信息";
            case "threshold":
                return "阈值配置";
            default:
                return "组件属性: " + attrName;
        }
    }

    /**
     * 组件键内部类
     */
    private static class ComponentKey {
        private final Long actId;
        private final Long cmptId;
        private final Long cmptUseInx;

        public ComponentKey(Long actId, Long cmptId, Long cmptUseInx) {
            this.actId = actId;
            this.cmptId = cmptId;
            this.cmptUseInx = cmptUseInx;
        }

        public Long getActId() {
            return actId;
        }

        public Long getCmptId() {
            return cmptId;
        }

        public Long getCmptUseInx() {
            return cmptUseInx;
        }
    }

    /**
     * 根据活动ID删除组件属性（自动选择数据库）
     *
     * @param actId 活动ID
     * @return 删除的记录数
     */
    public int deleteByActId(Long actId) {
        try {
            // 根据活动ID自动选择数据库
            String dbName = HdzkDbUtil.getHdzkDbByActId(actId);
            int deleteCount = hdzjComponentAttrMapper.deleteByActId(dbName, actId);
            log.info("删除活动 {} 的组件属性记录 {} 条，数据库: {}", actId, deleteCount, dbName);
            return deleteCount;
        } catch (Exception e) {
            log.error("删除组件属性失败，活动ID: {}, 错误: {}", actId, e.getMessage(), e);
            throw new RuntimeException("删除组件属性失败: " + e.getMessage(), e);
        }
    }

    /**
     * 根据活动ID删除组件属性（指定数据库）
     *
     * @param dbName 数据库名称
     * @param actId  活动ID
     * @return 删除的记录数
     */
    public int deleteByActId(String dbName, Long actId) {
        try {
            int deleteCount = hdzjComponentAttrMapper.deleteByActId(dbName, actId);
            log.info("删除活动 {} 的组件属性记录 {} 条，数据库: {}", actId, deleteCount, dbName);
            return deleteCount;
        } catch (Exception e) {
            log.error("删除组件属性失败，活动ID: {}, 错误: {}", actId, e.getMessage(), e);
            throw new RuntimeException("删除组件属性失败: " + e.getMessage(), e);
        }
    }

}
