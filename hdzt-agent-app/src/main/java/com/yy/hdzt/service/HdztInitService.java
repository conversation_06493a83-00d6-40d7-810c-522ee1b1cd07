package com.yy.hdzt.service;

import com.yy.hdzt.template.ActConfigTemplate;
import com.yy.hdzt.template.ActRankConfigTemplate;
import com.yy.hdzt.template.ActRankPhaseTemplate;
import com.yy.hdzt.template.ActScheduleTemplate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.test.annotation.Rollback;
import org.springframework.transaction.annotation.Transactional;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
@Slf4j
public class HdztInitService {

    @Autowired
    private HdztActivityService hdztActivityService;

    @Autowired
    private HdztActivityAttrService hdztActivityAttrService;

    @Autowired
    private RankingPhaseGroupService rankingPhaseGroupService;

    @Autowired
    private RankingPhaseService rankingPhaseService;

    @Autowired
    private RankingItemTransformService rankingItemTransformService;

    @Autowired
    private RankingConfigService rankingConfigService;

    @Autowired
    private HdztEnrollPolicyService hdztEnrollPolicyService;

    @Autowired
    private RankingMemberService rankingMemberService;

    @Autowired
    private HdzjComponentAttrService hdzjComponentAttrService;

    @Transactional(rollbackFor = Exception.class)
    public void initAct(ActConfigTemplate actConfigTemplate) {
        long actId = Long.parseLong(actConfigTemplate.getActId());
        String hdztDb = actConfigTemplate.getHdztDb();
        hdztActivityService.initAct(actConfigTemplate);
        hdztActivityAttrService.insertGrayStatusAttr(hdztDb, actId);
        rankingPhaseGroupService.insertActSchedulesToPhaseGroup(hdztDb,
                actId, actConfigTemplate.getActSchedules());
        Map<String, List<ActRankPhaseTemplate>> schedulePhaseMap = generateSchedulePhaseMap(actConfigTemplate);
        rankingPhaseService.batchInsertMultipleSchedulePhases(hdztDb, actId, schedulePhaseMap);
        rankingItemTransformService.insertActGiftsToRankingConfig(hdztDb, actId, actConfigTemplate.getActGifts());

        // 按赛程分组处理榜单配置并插入
        Map<String, List<ActRankConfigTemplate>> scheduleRankConfigMap = generateScheduleRankConfigMap(actConfigTemplate);
        batchInsertMultipleScheduleRankConfigs(hdztDb, actId, scheduleRankConfigMap);

        // 按赛程分组处理计榜成员并插入
        batchInsertMultipleScheduleRankingMembers(hdztDb, actId, scheduleRankConfigMap);

        // 插入报名策略
        hdztEnrollPolicyService.insertActRolesToEnrollPolicy(hdztDb, actId, actConfigTemplate.getActRoles());
    }

    @Transactional(rollbackFor = Exception.class)
    public void initActComponent(String jsonContent) {

    }

    /**
     * 将 ActScheduleTemplate 列表按照 scheduleCode 生成 Map<String, List<ActRankPhaseTemplate>>
     *
     * @param actConfigTemplate 活动配置模板
     * @return Map<scheduleCode, List<ActRankPhaseTemplate>>
     */
    public static Map<String, List<ActRankPhaseTemplate>> generateSchedulePhaseMap(ActConfigTemplate actConfigTemplate) {
        if (actConfigTemplate == null) {
            log.warn("ActConfigTemplate 为空");
            return new HashMap<>();
        }
        return generateSchedulePhaseMap(actConfigTemplate.getActSchedules());
    }

    /**
     * 将 ActScheduleTemplate 列表按照 scheduleCode 生成 Map<String, List<ActRankPhaseTemplate>>
     *
     * @param actSchedules ActScheduleTemplate 列表
     * @return Map<scheduleCode, List<ActRankPhaseTemplate>>
     */
    public static Map<String, List<ActRankPhaseTemplate>> generateSchedulePhaseMap(List<ActScheduleTemplate> actSchedules) {
        Map<String, List<ActRankPhaseTemplate>> schedulePhaseMap = new HashMap<>();

        if (actSchedules == null || actSchedules.isEmpty()) {
            log.warn("ActScheduleTemplate 列表为空");
            return schedulePhaseMap;
        }

        for (ActScheduleTemplate schedule : actSchedules) {
            String scheduleCode = schedule.getScheduleCode();
            List<ActRankPhaseTemplate> actRankPhases = schedule.getActRankPhases();

            if (isValidScheduleCode(scheduleCode)) {
                if (actRankPhases != null && !actRankPhases.isEmpty()) {
                    // 创建新的列表副本，避免引用问题
                    List<ActRankPhaseTemplate> phasesCopy = new ArrayList<>(actRankPhases);
                    schedulePhaseMap.put(scheduleCode, phasesCopy);

                    log.debug("添加赛程阶段映射，scheduleCode: {}, 阶段数量: {}",
                            scheduleCode, phasesCopy.size());
                } else {
                    // 即使没有阶段，也添加空列表
                    schedulePhaseMap.put(scheduleCode, new ArrayList<>());
                    log.debug("添加赛程阶段映射（空阶段），scheduleCode: {}", scheduleCode);
                }
            } else {
                log.warn("赛程编码无效，跳过该赛程: {}", scheduleCode);
            }
        }

        log.info("生成赛程阶段映射完成，赛程数量: {}, 总阶段数量: {}",
                schedulePhaseMap.size(),
                schedulePhaseMap.values().stream().mapToInt(List::size).sum());

        return schedulePhaseMap;
    }

    /**
     * 验证赛程编码是否有效
     *
     * @param scheduleCode 赛程编码
     * @return 是否有效
     */
    private static boolean isValidScheduleCode(String scheduleCode) {
        return scheduleCode != null && !scheduleCode.trim().isEmpty();
    }

    /**
     * 按赛程分组批量插入榜单配置
     *
     * @param dbName 数据库名称
     * @param actId 活动ID
     * @param scheduleRankConfigMap 按赛程分组的榜单配置映射
     * @return 总插入数量
     */
    public int batchInsertMultipleScheduleRankConfigs(String dbName, Long actId,
                                                     Map<String, List<ActRankConfigTemplate>> scheduleRankConfigMap) {
        try {
            int totalCount = 0;

            for (Map.Entry<String, List<ActRankConfigTemplate>> entry : scheduleRankConfigMap.entrySet()) {
                String scheduleCode = entry.getKey();
                List<ActRankConfigTemplate> rankConfigs = entry.getValue();

                int count = rankingConfigService.insertActRankConfigsToRankingConfig(dbName, actId, scheduleCode, rankConfigs);
                totalCount += count;

                log.info("赛程 {} 的榜单配置插入完成，数量: {}", scheduleCode, count);
            }

            log.info("多个赛程的榜单配置批量插入完成，活动ID: {}, 总数量: {}, 数据库: {}",
                    actId, totalCount, dbName);

            return totalCount;

        } catch (Exception e) {
            log.error("批量插入多个赛程的榜单配置失败: {}", e.getMessage(), e);
            throw new RuntimeException("批量插入多个赛程的榜单配置失败: " + e.getMessage(), e);
        }
    }

    /**
     * 按赛程分组批量插入计榜成员
     *
     * @param dbName 数据库名称
     * @param actId 活动ID
     * @param scheduleRankConfigMap 按赛程分组的榜单配置映射
     * @return 总插入数量
     */
    public int batchInsertMultipleScheduleRankingMembers(String dbName, Long actId,
                                                        Map<String, List<ActRankConfigTemplate>> scheduleRankConfigMap) {
        try {
            int totalCount = 0;

            for (Map.Entry<String, List<ActRankConfigTemplate>> entry : scheduleRankConfigMap.entrySet()) {
                String scheduleCode = entry.getKey();
                List<ActRankConfigTemplate> rankConfigs = entry.getValue();

                if (rankConfigs != null && !rankConfigs.isEmpty()) {
                    int count = rankingMemberService.insertActRankConfigsToRankingMember(dbName, actId, rankConfigs);
                    totalCount += count;

                    log.info("赛程 {} 的计榜成员插入完成，数量: {}", scheduleCode, count);
                } else {
                    log.debug("赛程 {} 无榜单配置，跳过计榜成员插入", scheduleCode);
                }
            }

            log.info("多个赛程的计榜成员批量插入完成，活动ID: {}, 总数量: {}, 数据库: {}",
                    actId, totalCount, dbName);

            return totalCount;

        } catch (Exception e) {
            log.error("批量插入多个赛程的计榜成员失败: {}", e.getMessage(), e);
            throw new RuntimeException("批量插入多个赛程的计榜成员失败: " + e.getMessage(), e);
        }
    }

    /**
     * 将 ActScheduleTemplate 列表按照 scheduleCode 生成 Map<String, List<ActRankConfigTemplate>>
     *
     * @param actConfigTemplate 活动配置模板
     * @return Map<scheduleCode, List<ActRankConfigTemplate>>
     */
    public static Map<String, List<ActRankConfigTemplate>> generateScheduleRankConfigMap(ActConfigTemplate actConfigTemplate) {
        if (actConfigTemplate == null) {
            log.warn("ActConfigTemplate 为空");
            return new HashMap<>();
        }

        return generateScheduleRankConfigMap(actConfigTemplate.getActSchedules());
    }

    /**
     * 将 ActScheduleTemplate 列表按照 scheduleCode 生成 Map<String, List<ActRankConfigTemplate>>
     *
     * @param actSchedules ActScheduleTemplate 列表
     * @return Map<scheduleCode, List<ActRankConfigTemplate>>
     */
    public static Map<String, List<ActRankConfigTemplate>> generateScheduleRankConfigMap(List<ActScheduleTemplate> actSchedules) {
        Map<String, List<ActRankConfigTemplate>> scheduleRankConfigMap = new HashMap<>();

        if (actSchedules == null || actSchedules.isEmpty()) {
            log.warn("ActScheduleTemplate 列表为空");
            return scheduleRankConfigMap;
        }

        for (ActScheduleTemplate schedule : actSchedules) {
            String scheduleCode = schedule.getScheduleCode();
            List<ActRankConfigTemplate> actRankConfigs = schedule.getActRankConfigs();

            if (isValidScheduleCode(scheduleCode)) {
                if (actRankConfigs != null && !actRankConfigs.isEmpty()) {
                    // 创建新的列表副本，避免引用问题
                    List<ActRankConfigTemplate> rankConfigsCopy = new ArrayList<>(actRankConfigs);
                    scheduleRankConfigMap.put(scheduleCode, rankConfigsCopy);

                    log.debug("添加赛程榜单配置映射，scheduleCode: {}, 榜单配置数量: {}",
                            scheduleCode, rankConfigsCopy.size());
                } else {
                    // 即使没有榜单配置，也添加空列表
                    scheduleRankConfigMap.put(scheduleCode, new ArrayList<>());
                    log.debug("添加赛程榜单配置映射（空配置），scheduleCode: {}", scheduleCode);
                }
            } else {
                log.warn("赛程编码无效，跳过该赛程的榜单配置: {}", scheduleCode);
            }
        }

        log.info("生成赛程榜单配置映射完成，赛程数量: {}, 总榜单配置数量: {}",
                scheduleRankConfigMap.size(),
                scheduleRankConfigMap.values().stream().mapToInt(List::size).sum());

        return scheduleRankConfigMap;
    }

}


