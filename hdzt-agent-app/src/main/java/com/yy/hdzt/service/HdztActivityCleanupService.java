package com.yy.hdzt.service;

import com.yy.hdzt.mapper.hdzk.HdzjComponentAttrMapper;
import com.yy.hdzt.mapper.hdzt.*;
import com.yy.hdzt.mapper.hdzk.HdzjComponentAttrMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 活动数据清理服务
 * 提供根据数据库名称和活动ID删除相关数据的功能
 * 
 * <AUTHOR> 2025/7/23
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class HdztActivityCleanupService {

    private final HdztActivityMapper hdztActivityMapper;
    private final HdztActivityAttrMapper hdztActivityAttrMapper;
    private final HdztEnrollPolicyMapper hdztEnrollPolicyMapper;
    private final RankingConfigMapper rankingConfigMapper;
    private final RankingPhaseMapper rankingPhaseMapper;
    private final RankingPhaseGroupMapper rankingPhaseGroupMapper;
    private final RankingMemberMapper rankingMemberMapper;
    private final RankingItemMapper rankingItemMapper;
    private final RankingItemTransformMapper rankingItemTransformMapper;
    private final HdzjComponentAttrMapper hdzjComponentAttrMapper;

    /**
     * 删除指定活动的所有相关数据
     * 
     * @param dbName 数据库名称
     * @param actId 活动ID
     * @return 删除结果统计
     */
    @Transactional(rollbackFor = Exception.class)
    public ActivityCleanupResult deleteActivityData(String dbName, Long actId) {
        log.info("开始删除活动数据 - 数据库: {}, 活动ID: {}", dbName, actId);
        
        ActivityCleanupResult result = new ActivityCleanupResult();
        result.setDbName(dbName);
        result.setActId(actId);
        
        try {
            // 删除计榜项目
            int rankingItemCount = rankingItemMapper.deleteByActId(dbName, actId);
            result.setRankingItemCount(rankingItemCount);
            log.info("删除计榜项目 {} 条", rankingItemCount);
            
            // 删除项目标识转换
            int rankingItemTransformCount = rankingItemTransformMapper.deleteByActId(dbName, actId);
            result.setRankingItemTransformCount(rankingItemTransformCount);
            log.info("删除项目标识转换 {} 条", rankingItemTransformCount);

            // 删除组件属性
            int componentAttrCount = hdzjComponentAttrMapper.deleteByActId(dbName, actId);
            result.setComponentAttrCount(componentAttrCount);
            log.info("删除组件属性 {} 条", componentAttrCount);
            
            // 删除计榜成员
            int rankingMemberCount = rankingMemberMapper.deleteByActId(dbName, actId);
            result.setRankingMemberCount(rankingMemberCount);
            log.info("删除计榜成员 {} 条", rankingMemberCount);
            
            // 删除排行榜阶段
            int rankingPhaseCount = rankingPhaseMapper.deleteByActId(dbName, actId);
            result.setRankingPhaseCount(rankingPhaseCount);
            log.info("删除排行榜阶段 {} 条", rankingPhaseCount);
            
            // 删除阶段分组
            int rankingPhaseGroupCount = rankingPhaseGroupMapper.deleteByActId(dbName, actId);
            result.setRankingPhaseGroupCount(rankingPhaseGroupCount);
            log.info("删除阶段分组 {} 条", rankingPhaseGroupCount);
            
            // 删除榜单配置
            int rankingConfigCount = rankingConfigMapper.deleteByActId(dbName, actId);
            result.setRankingConfigCount(rankingConfigCount);
            log.info("删除榜单配置 {} 条", rankingConfigCount);
            
            // 删除报名策略
            int enrollPolicyCount = hdztEnrollPolicyMapper.deleteByActId(dbName, actId);
            result.setEnrollPolicyCount(enrollPolicyCount);
            log.info("删除报名策略 {} 条", enrollPolicyCount);
            
            // 删除活动属性
            int activityAttrCount = hdztActivityAttrMapper.deleteByActId(dbName, actId);
            result.setActivityAttrCount(activityAttrCount);
            log.info("删除活动属性 {} 条", activityAttrCount);
            
            // 删除活动信息
            int activityCount = hdztActivityMapper.deleteByActId(dbName, actId);
            result.setActivityCount(activityCount);
            log.info("删除活动信息 {} 条", activityCount);
            
            result.setSuccess(true);
            result.setMessage("删除成功");
            
            log.info("活动数据删除完成 - 数据库: {}, 活动ID: {}, 总计删除: {} 条", 
                    dbName, actId, result.getTotalCount());
            
        } catch (Exception e) {
            log.error("删除活动数据失败 - 数据库: {}, 活动ID: {}", dbName, actId, e);
            result.setSuccess(false);
            result.setMessage("删除失败: " + e.getMessage());
            throw e; // 重新抛出异常以触发事务回滚
        }
        
        return result;
    }
    /**
     * 活动清理结果
     */
    public static class ActivityCleanupResult {
        private String dbName;
        private Long actId;
        private boolean success;
        private String message;
        private int activityCount;
        private int activityAttrCount;
        private int enrollPolicyCount;
        private int rankingConfigCount;
        private int rankingPhaseCount;
        private int rankingPhaseGroupCount;
        private int rankingMemberCount;
        private int rankingItemCount;
        private int rankingItemTransformCount;
        private int componentAttrCount;

        // Getters and Setters
        public String getDbName() { return dbName; }
        public void setDbName(String dbName) { this.dbName = dbName; }

        public Long getActId() { return actId; }
        public void setActId(Long actId) { this.actId = actId; }

        public boolean isSuccess() { return success; }
        public void setSuccess(boolean success) { this.success = success; }

        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }

        public int getActivityCount() { return activityCount; }
        public void setActivityCount(int activityCount) { this.activityCount = activityCount; }

        public int getActivityAttrCount() { return activityAttrCount; }
        public void setActivityAttrCount(int activityAttrCount) { this.activityAttrCount = activityAttrCount; }

        public int getEnrollPolicyCount() { return enrollPolicyCount; }
        public void setEnrollPolicyCount(int enrollPolicyCount) { this.enrollPolicyCount = enrollPolicyCount; }

        public int getRankingConfigCount() { return rankingConfigCount; }
        public void setRankingConfigCount(int rankingConfigCount) { this.rankingConfigCount = rankingConfigCount; }

        public int getRankingPhaseCount() { return rankingPhaseCount; }
        public void setRankingPhaseCount(int rankingPhaseCount) { this.rankingPhaseCount = rankingPhaseCount; }

        public int getRankingPhaseGroupCount() { return rankingPhaseGroupCount; }
        public void setRankingPhaseGroupCount(int rankingPhaseGroupCount) { this.rankingPhaseGroupCount = rankingPhaseGroupCount; }

        public int getRankingMemberCount() { return rankingMemberCount; }
        public void setRankingMemberCount(int rankingMemberCount) { this.rankingMemberCount = rankingMemberCount; }

        public int getRankingItemCount() { return rankingItemCount; }
        public void setRankingItemCount(int rankingItemCount) { this.rankingItemCount = rankingItemCount; }

        public int getRankingItemTransformCount() { return rankingItemTransformCount; }
        public void setRankingItemTransformCount(int rankingItemTransformCount) { this.rankingItemTransformCount = rankingItemTransformCount; }

        public int getComponentAttrCount() { return componentAttrCount; }
        public void setComponentAttrCount(int componentAttrCount) { this.componentAttrCount = componentAttrCount; }

        /**
         * 获取总删除记录数
         */
        public int getTotalCount() {
            return activityCount + activityAttrCount + enrollPolicyCount + rankingConfigCount +
                   rankingPhaseCount + rankingPhaseGroupCount + rankingMemberCount +
                   rankingItemCount + rankingItemTransformCount + componentAttrCount;
        }
    }
}
