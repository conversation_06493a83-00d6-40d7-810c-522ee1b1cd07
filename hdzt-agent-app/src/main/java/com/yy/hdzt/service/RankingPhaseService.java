package com.yy.hdzt.service;

import com.yy.hdzt.bean.hdzt.RankingPhase;
import com.yy.hdzt.mapper.hdzt.RankingPhaseMapper;
import com.yy.hdzt.template.ActRankPhaseTemplate;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * ActRankPhaseTemplate 转换为 RankingPhase 服务类
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class RankingPhaseService {
    
    private final RankingPhaseMapper rankingPhaseMapper;
    
    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    public int batchInsertMultipleSchedulePhases(String dbName, Long actId,
                                                 java.util.Map<String, List<ActRankPhaseTemplate>> schedulePhaseMap) {
        try {
            int totalCount = 0;

            for (java.util.Map.Entry<String, List<ActRankPhaseTemplate>> entry : schedulePhaseMap.entrySet()) {
                String phaseGroupCode = entry.getKey();
                List<ActRankPhaseTemplate> phases = entry.getValue();

                int count = insertActRankPhasesToRankingPhase(dbName, actId, phaseGroupCode, phases);
                totalCount += count;

                log.info("赛程 {} 的阶段插入完成，数量: {}", phaseGroupCode, count);
            }

            log.info("多个赛程的阶段批量插入完成，活动ID: {}, 总数量: {}, 数据库: {}",
                    actId, totalCount, dbName);

            return totalCount;

        } catch (Exception e) {
            log.error("批量插入多个赛程的阶段失败: {}", e.getMessage(), e);
            throw new RuntimeException("批量插入多个赛程的阶段失败: " + e.getMessage(), e);
        }
    }

    public int insertActRankPhasesToRankingPhase(String dbName, Long actId, String phaseGroupCode,
                                               List<ActRankPhaseTemplate> actRankPhases) {
        try {
            if (actRankPhases == null || actRankPhases.isEmpty()) {
                log.warn("ActRankPhaseTemplate 列表为空，无法插入");
                return 0;
            }
            
            // 转换 ActRankPhaseTemplate 为 RankingPhase
            List<RankingPhase> rankingPhases = convertToRankingPhases(actId, phaseGroupCode, actRankPhases);
            
            if (rankingPhases.isEmpty()) {
                log.warn("转换后的 RankingPhase 列表为空");
                return 0;
            }
            
            // 批量插入
            int result = rankingPhaseMapper.batchInsertPhases(dbName, rankingPhases);
            
            log.info("ActRankPhaseTemplate 列表插入完成，活动ID: {}, 成功数量: {}, 数据库: {}", 
                actId, result, dbName);
            
            return result;
            
        } catch (Exception e) {
            log.error("插入 ActRankPhaseTemplate 列表失败: {}", e.getMessage(), e);
            throw new RuntimeException("插入 ActRankPhaseTemplate 列表失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 将 ActRankPhaseTemplate 列表转换为 RankingPhase 列表
     * 
     * @param actId 活动ID
     * @param phaseGroupCode 阶段分组编码
     * @param actRankPhases ActRankPhaseTemplate 列表
     * @return RankingPhase 列表
     */
    private List<RankingPhase> convertToRankingPhases(Long actId, String phaseGroupCode,
                                                    List<ActRankPhaseTemplate> actRankPhases) {
        List<RankingPhase> rankingPhases = new ArrayList<>();
        
        for (ActRankPhaseTemplate template : actRankPhases) {
            try {
                RankingPhase rankingPhase = convertToRankingPhase(actId, phaseGroupCode, template);
                rankingPhases.add(rankingPhase);
                
                log.debug("转换阶段模板成功，阶段ID: {}, 阶段名称: {}", 
                    template.getPhaseId(), template.getPhaseName());
                
            } catch (Exception e) {
                log.error("转换阶段模板失败，阶段ID: {}, 错误: {}", template.getPhaseId(), e.getMessage());
                // 继续处理其他阶段，不中断整个流程
            }
        }
        
        return rankingPhases;
    }
    
    /**
     * 将单个 ActRankPhaseTemplate 转换为 RankingPhase
     * 
     * @param actId 活动ID
     * @param phaseGroupCode 阶段分组编码
     * @param template ActRankPhaseTemplate
     * @return RankingPhase
     */
    private RankingPhase convertToRankingPhase(Long actId, String phaseGroupCode, ActRankPhaseTemplate template) {
        RankingPhase rankingPhase = new RankingPhase();
        
        // 设置活动ID
        rankingPhase.setActId(actId);
        
        // 设置阶段ID
        try {
            rankingPhase.setPhaseId(Integer.parseInt(template.getPhaseId()));
        } catch (NumberFormatException e) {
            log.warn("阶段ID格式错误: {}, 使用默认值", template.getPhaseId());
            rankingPhase.setPhaseId(1);
        }
        
        // 设置阶段名称
        rankingPhase.setPhaseName(template.getPhaseName());
        rankingPhase.setPhaseNameShow(template.getPhaseName());
        
        // 设置阶段分组编码
        rankingPhase.setPhaseGroupCode(phaseGroupCode);
        
        // 设置状态为有效
        rankingPhase.setStatus((byte) 1);
        
        // 解析时间
        try {
            if (template.getBeginTime() != null && !template.getBeginTime().trim().isEmpty()) {
                Date beginTime = parseDateTime(template.getBeginTime());
                rankingPhase.setBeginTime(beginTime);
                rankingPhase.setShowBeginTime(beginTime);
            }
            
            if (template.getEndTime() != null && !template.getEndTime().trim().isEmpty()) {
                Date endTime = parseDateTime(template.getEndTime());
                rankingPhase.setEndTime(endTime);
                rankingPhase.setShowEndTime(endTime);
            }
        } catch (Exception e) {
            log.warn("解析时间失败，阶段ID: {}, 错误: {}", template.getPhaseId(), e.getMessage());
        }
        
        // 设置默认值
        rankingPhase.setTaskValueType(1);
        rankingPhase.setRecycleCount(0);
        rankingPhase.setRecycleType(1);
        
        // 设置备注
        rankingPhase.setRemark(String.format("从阶段模板转换，原阶段ID: %s", template.getPhaseId()));
        
        // 设置扩展数据
        String extJson = String.format("{\"templatePhaseId\":\"%s\",\"templatePhaseName\":\"%s\"}", 
            template.getPhaseId(), template.getPhaseName());
        rankingPhase.setExtjson(extJson);
        
        return rankingPhase;
    }
    
    /**
     * 解析日期时间字符串
     * 
     * @param dateTimeStr 日期时间字符串
     * @return Date 对象
     */
    private Date parseDateTime(String dateTimeStr) throws ParseException {
        // 尝试多种日期格式
        String[] formats = {
            "yyyy-MM-dd HH:mm:ss",
            "yyyy-MM-dd",
            "yyyy/MM/dd HH:mm:ss",
            "yyyy/MM/dd"
        };
        
        for (String format : formats) {
            try {
                SimpleDateFormat sdf = new SimpleDateFormat(format);
                return sdf.parse(dateTimeStr.trim());
            } catch (ParseException e) {
                // 继续尝试下一种格式
            }
        }
        throw new ParseException("无法解析日期时间: " + dateTimeStr, 0);
    }
}
