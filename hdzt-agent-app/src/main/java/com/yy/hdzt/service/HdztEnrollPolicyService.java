package com.yy.hdzt.service;

import com.yy.hdzt.bean.hdzt.HdztEnrollPolicy;
import com.yy.hdzt.mapper.hdzt.HdztEnrollPolicyMapper;
import com.yy.hdzt.template.ActRoleTemplate;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.util.ArrayList;
import java.util.List;

/**
 * ActRoleTemplate 转换为 HdztEnrollPolicy 服务类
 */
@Service
@Slf4j
public class HdztEnrollPolicyService {

    @Resource
    private HdztEnrollPolicyMapper hdztEnrollPolicyMapper;

    /**
     * 将 ActRoleTemplate 列表插入到 hdzt_enroll_policy 表中
     *
     * @param dbName 数据库名称
     * @param actId 活动ID
     * @param actRoles ActRoleTemplate 列表
     * @return 成功插入的数量
     */
    public int insertActRolesToEnrollPolicy(String dbName, Long actId, List<ActRoleTemplate> actRoles) {
        try {
            if (actRoles == null || actRoles.isEmpty()) {
                log.warn("ActRoleTemplate 列表为空，无法插入");
                return 0;
            }
            // 转换 ActRoleTemplate 为 HdztEnrollPolicy
            List<HdztEnrollPolicy> enrollPolicies = convertToEnrollPolicies(actId, actRoles);

            if (enrollPolicies.isEmpty()) {
                log.warn("转换后的 HdztEnrollPolicy 列表为空");
                return 0;
            }

            // 批量插入
            int result = hdztEnrollPolicyMapper.batchInsertEnrollPolicies(dbName, enrollPolicies);

            log.info("ActRoleTemplate 列表插入完成，活动ID: {}, 成功数量: {}, 数据库: {}",
                actId, result, dbName);
            return result;
        } catch (Exception e) {
            log.error("插入 ActRoleTemplate 列表失败: {}", e.getMessage(), e);
            throw new RuntimeException("插入 ActRoleTemplate 列表失败: " + e.getMessage(), e);
        }
    }

    /**
     * 将 ActRoleTemplate 列表转换为 HdztEnrollPolicy 列表
     *
     * @param actId 活动ID
     * @param actRoles ActRoleTemplate 列表
     * @return HdztEnrollPolicy 列表
     */
    private List<HdztEnrollPolicy> convertToEnrollPolicies(Long actId, List<ActRoleTemplate> actRoles) {
        List<HdztEnrollPolicy> enrollPolicies = new ArrayList<>();

        for (ActRoleTemplate template : actRoles) {
            try {
                HdztEnrollPolicy enrollPolicy = convertToEnrollPolicy(actId, template);
                enrollPolicies.add(enrollPolicy);

                log.debug("转换角色模板成功，角色ID: {}, 备注: {}",
                    template.getRoleId(), template.getRemark());

            } catch (Exception e) {
                log.error("转换角色模板失败，角色ID: {}, 错误: {}", template.getRoleId(), e.getMessage());
                // 继续处理其他角色，不中断整个流程
            }
        }

        return enrollPolicies;
    }

    /**
     * 将单个 ActRoleTemplate 转换为 HdztEnrollPolicy
     *
     * @param actId 活动ID
     * @param template ActRoleTemplate
     * @return HdztEnrollPolicy
     */
    private HdztEnrollPolicy convertToEnrollPolicy(Long actId, ActRoleTemplate template) {
        HdztEnrollPolicy enrollPolicy = new HdztEnrollPolicy();

        // 设置活动ID
        enrollPolicy.setActId(actId.intValue());

        // 设置角色ID
        try {
            enrollPolicy.setRoleId(Integer.parseInt(template.getRoleId()));
        } catch (NumberFormatException e) {
            log.warn("角色ID格式错误: {}, 使用默认值", template.getRoleId());
            enrollPolicy.setRoleId(800);
        }

        // 设置报名检查标志
        try {
            enrollPolicy.setCheckFlag(Integer.parseInt(template.getCheckFlag()));
        } catch (NumberFormatException e) {
            log.warn("报名检查标志格式错误: {}, 使用默认值0", template.getCheckFlag());
            enrollPolicy.setCheckFlag(0);
        }

        // 设置转换标志
        try {
            enrollPolicy.setConvertFlag(Integer.parseInt(template.getConvertFlag()));
        } catch (NumberFormatException e) {
            log.warn("转换标志格式错误: {}, 使用默认值0", template.getConvertFlag());
            enrollPolicy.setConvertFlag(0);
        }

        // 设置通过方式
        try {
            enrollPolicy.setPassWay(Integer.parseInt(template.getPassWay()));
        } catch (NumberFormatException e) {
            log.warn("通过方式格式错误: {}, 使用默认值1", template.getPassWay());
            enrollPolicy.setPassWay(1);
        }

        // 设置自动报名
        try {
            enrollPolicy.setAutoEnroll(Integer.parseInt(template.getAutoEnroll()));
        } catch (NumberFormatException e) {
            log.warn("自动报名格式错误: {}, 使用默认值1", template.getAutoEnroll());
            enrollPolicy.setAutoEnroll(1);
        }

        // 设置备注
        enrollPolicy.setRemark(template.getRemark());

        // 设置扩展数据
        String extJson = String.format(
            "{\"templateRoleId\":\"%s\",\"templateRemark\":\"%s\"}",
            template.getRoleId(), template.getRemark());
        enrollPolicy.setExtjson(extJson);

        return enrollPolicy;
    }
}
