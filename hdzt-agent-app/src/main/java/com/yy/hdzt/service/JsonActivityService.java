package com.yy.hdzt.service;

import com.yy.hdzt.bean.hdzt.HdztActivity;
import com.yy.hdzt.mapper.hdzt.HdztActivityMapper;
import com.yy.hdzt.template.ActConfigTemplate;
import com.yy.hdzt.util.ActConfigJsonParser;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * JSON 活动处理服务
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class JsonActivityService {
    private final HdztActivityMapper hdztActivityMapper;
    
    /**
     * 解析业务ID
     */
    private Integer parseBusiId(String busiIdStr) {
        try {
            // 如果是纯数字，直接解析
            return Integer.parseInt(busiIdStr);
        } catch (NumberFormatException e) {
            // 如果包含描述文字，提取数字
            if (busiIdStr.contains("810")) {
                return 810;
            } else if (busiIdStr.contains("500")) {
                return 500;
            } else {
                // 默认值
                log.warn("无法解析业务ID: {}, 使用默认值 810", busiIdStr);
                return 810;
            }
        }
    }

}
