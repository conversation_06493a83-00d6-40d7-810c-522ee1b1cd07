package com.yy.hdzt.service;

import com.yy.hdzt.bean.hdzt.RankingMember;
import com.yy.hdzt.mapper.hdzt.RankingMemberMapper;
import com.yy.hdzt.template.ActRankConfigTemplate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.util.ArrayList;
import java.util.List;

/**
 * ActRankConfigTemplate 转换为 RankingMember 服务类
 */
@Service
@Slf4j
public class RankingMemberService {

    @Autowired
    private RankingMemberMapper rankingMemberMapper;

    /**
     * 将 ActRankConfigTemplate 列表插入到 ranking_member 表中
     *
     * @param dbName 数据库名称
     * @param actId 活动ID
     * @param actRankConfigs ActRankConfigTemplate 列表
     * @return 成功插入的数量
     */
    public int insertActRankConfigsToRankingMember(String dbName, Long actId,
                                                  List<ActRankConfigTemplate> actRankConfigs) {
        try {
            if (actRankConfigs == null || actRankConfigs.isEmpty()) {
                log.warn("ActRankConfigTemplate 列表为空，无法插入");
                return 0;
            }

            // 转换 ActRankConfigTemplate 为 RankingMember
            List<RankingMember> rankingMembers = convertToRankingMembers(actId, actRankConfigs);

            if (rankingMembers.isEmpty()) {
                log.warn("转换后的 RankingMember 列表为空");
                return 0;
            }

            // 批量插入
            int result = rankingMemberMapper.batchInsertRankingMembers(dbName, rankingMembers);

            log.info("ActRankConfigTemplate 列表插入完成，活动ID: {}, 成功数量: {}, 数据库: {}",
                actId, result, dbName);

            return result;

        } catch (Exception e) {
            log.error("插入 ActRankConfigTemplate 列表失败: {}", e.getMessage(), e);
            throw new RuntimeException("插入 ActRankConfigTemplate 列表失败: " + e.getMessage(), e);
        }
    }

    /**
     * 将 ActRankConfigTemplate 列表转换为 RankingMember 列表
     *
     * @param actId 活动ID
     * @param actRankConfigs ActRankConfigTemplate 列表
     * @return RankingMember 列表
     */
    private List<RankingMember> convertToRankingMembers(Long actId, List<ActRankConfigTemplate> actRankConfigs) {
        List<RankingMember> rankingMembers = new ArrayList<>();

        for (ActRankConfigTemplate template : actRankConfigs) {
            try {
                RankingMember rankingMember = convertToRankingMember(actId, template);
                rankingMembers.add(rankingMember);

                log.debug("转换榜单配置模板成功，榜单ID: {}, 榜单名称: {}",
                    template.getRankId(), template.getRankName());

            } catch (Exception e) {
                log.error("转换榜单配置模板失败，榜单ID: {}, 错误: {}", template.getRankId(), e.getMessage());
                // 继续处理其他配置，不中断整个流程
            }
        }

        return rankingMembers;
    }

    /**
     * 将单个 ActRankConfigTemplate 转换为 RankingMember
     *
     * @param actId 活动ID
     * @param template ActRankConfigTemplate
     * @return RankingMember
     */
    private RankingMember convertToRankingMember(Long actId, ActRankConfigTemplate template) {
        RankingMember rankingMember = new RankingMember();

        // 设置活动ID
        rankingMember.setActId(actId.intValue());

        // 设置榜单ID
        try {
            rankingMember.setRankId(Integer.parseInt(template.getRankId()));
        } catch (NumberFormatException e) {
            log.warn("榜单ID格式错误: {}, 使用默认值", template.getRankId());
            rankingMember.setRankId(1);
        }

        // 设置参与角色 - 只使用 roles 字段
        String roles = processRolesString(template.getRoles());
        rankingMember.setRoles(roles);

        // 设置备注
        String remark = String.format("榜单: %s, 角色: %s",
            template.getRankName(), template.getRoles());
        rankingMember.setRemark(remark);

        // 设置扩展数据
        String extJson = String.format(
            "{\"templateRankId\":\"%s\",\"templateRankName\":\"%s\",\"originalRoles\":\"%s\"}",
            template.getRankId(), template.getRankName(), template.getRoles());
        rankingMember.setExtjson(extJson);

        return rankingMember;
    }

    /**
     * 处理角色字符串，将文字描述转换为角色ID表达式
     *
     * @param rolesString 角色字符串
     * @return 处理后的角色表达式
     */
    private String processRolesString(String rolesString) {
        if (rolesString == null || rolesString.trim().isEmpty()) {
            // 如果角色为空，使用默认角色
            return "0"; // 默认主播或观众
        }

        // 简单的角色映射，将文字描述转换为角色ID
        String processed = rolesString.trim();

        // 如果已经是数字格式（如 "4001|4002"），直接返回
        if (processed.matches("[0-9|&]+")) {
            return processed;
        }
        // 如果没有找到映射，尝试直接解析数字
        if (!processed.matches(".*\\d+.*")) {
            log.warn("无法解析角色字符串: {}, 使用默认角色", rolesString);
            return "0";
        }

        return processed;
    }
}
