package com.yy.hdzt.service;

import com.yy.hdzt.bean.hdzt.RankingConfig;
import com.yy.hdzt.mapper.hdzt.RankingConfigMapper;
import com.yy.hdzt.template.ActRankConfigTemplate;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * ActRankConfigTemplate 转换为 RankingConfig 服务类
 */
@Service
@Slf4j
public class RankingConfigService {

    @Resource
    private RankingConfigMapper rankingConfigMapper;

    /**
     * 将 ActRankConfigTemplate 列表插入到 ranking_config 表中
     *
     * @param dbName 数据库名称
     * @param actId 活动ID
     * @param phaseGroupCode 阶段分组编码
     * @param actRankConfigs ActRankConfigTemplate 列表
     * @return 成功插入的数量
     */
    public int insertActRankConfigsToRankingConfig(String dbName, Long actId, String phaseGroupCode,
                                                  List<ActRankConfigTemplate> actRankConfigs) {
        try {
            if (actRankConfigs == null || actRankConfigs.isEmpty()) {
                log.warn("ActRankConfigTemplate 列表为空，无法插入");
                return 0;
            }

            // 转换 ActRankConfigTemplate 为 RankingConfig
            List<RankingConfig> rankingConfigs = convertToRankingConfigs(actId, phaseGroupCode, actRankConfigs);

            if (rankingConfigs.isEmpty()) {
                log.warn("转换后的 RankingConfig 列表为空");
                return 0;
            }

            // 批量插入
            int result = rankingConfigMapper.batchInsertRankingConfigs(dbName, rankingConfigs);

            log.info("ActRankConfigTemplate 列表插入完成，活动ID: {}, 阶段分组: {}, 成功数量: {}, 数据库: {}",
                actId, phaseGroupCode, result, dbName);

            return result;

        } catch (Exception e) {
            log.error("插入 ActRankConfigTemplate 列表失败: {}", e.getMessage(), e);
            throw new RuntimeException("插入 ActRankConfigTemplate 列表失败: " + e.getMessage(), e);
        }
    }

    /**
     * 将 ActRankConfigTemplate 列表转换为 RankingConfig 列表
     *
     * @param actId 活动ID
     * @param phaseGroupCode 阶段分组编码
     * @param actRankConfigs ActRankConfigTemplate 列表
     * @return RankingConfig 列表
     */
    private List<RankingConfig> convertToRankingConfigs(Long actId, String phaseGroupCode,
                                                       List<ActRankConfigTemplate> actRankConfigs) {
        List<RankingConfig> rankingConfigs = new ArrayList<>();

        for (ActRankConfigTemplate template : actRankConfigs) {
            try {
                RankingConfig rankingConfig = convertToRankingConfig(actId, phaseGroupCode, template);
                rankingConfigs.add(rankingConfig);

                log.debug("转换榜单配置模板成功，榜单ID: {}, 榜单名称: {}",
                    template.getRankId(), template.getRankName());

            } catch (Exception e) {
                log.error("转换榜单配置模板失败，榜单ID: {}, 错误: {}", template.getRankId(), e.getMessage());
                // 继续处理其他配置，不中断整个流程
            }
        }

        return rankingConfigs;
    }

    /**
     * 将单个 ActRankConfigTemplate 转换为 RankingConfig
     *
     * @param actId 活动ID
     * @param phaseGroupCode 阶段分组编码
     * @param template ActRankConfigTemplate
     * @return RankingConfig
     */
    private RankingConfig convertToRankingConfig(Long actId, String phaseGroupCode, ActRankConfigTemplate template) {
        RankingConfig rankingConfig = new RankingConfig();

        // 设置活动ID
        rankingConfig.setActId(actId.intValue());

        // 设置榜单ID
        try {
            rankingConfig.setRankId(Integer.parseInt(template.getRankId()));
        } catch (NumberFormatException e) {
            log.warn("榜单ID格式错误: {}, 使用默认值", template.getRankId());
            rankingConfig.setRankId(1);
        }

        // 设置榜单名称
        rankingConfig.setRankName(template.getRankName());
        rankingConfig.setRankNameShow(template.getRankName());

        // 设置榜单类型为通用类型
        rankingConfig.setRankType(0);

        // 设置状态为有效
        rankingConfig.setStatus((byte) 1);

        // 解析时间
        try {
            if (template.getBeginTime() != null && !template.getBeginTime().trim().isEmpty()) {
                Date beginTime = parseDateTime(template.getBeginTime());
                rankingConfig.setCalcBeginTime(beginTime);
                rankingConfig.setShowBeginTime(beginTime);
            }

            if (template.getEndTime() != null && !template.getEndTime().trim().isEmpty()) {
                Date endTime = parseDateTime(template.getEndTime());
                rankingConfig.setCalcEndTime(endTime);
                rankingConfig.setShowEndTime(endTime);
            }
        } catch (Exception e) {
            log.warn("解析时间失败，榜单ID: {}, 错误: {}", template.getRankId(), e.getMessage());
        }

        // 设置计值类型为客观数量
        rankingConfig.setValueType(1);

        // 设置项目限定为不限制
        rankingConfig.setLimitItem(0);

        // 设置时间分榜
        try {
            rankingConfig.setTimeKey(Byte.parseByte(template.getTimeKey()));
        } catch (NumberFormatException e) {
            log.warn("时间分榜格式错误: {}, 使用默认值0", template.getTimeKey());
            rankingConfig.setTimeKey((byte) 0);
        }

        // 设置时间分榜的开始和结束时间（如果需要按时间分榜）
        if (rankingConfig.getTimeKey() > 0) {
            rankingConfig.setTimeKeyBegin("00:00:00");
            rankingConfig.setTimeKeyEnd("23:59:59");
        }

        // 设置项目分榜
        try {
            rankingConfig.setItemKey(Byte.parseByte(template.getItemKey()));
        } catch (NumberFormatException e) {
            log.warn("项目分榜格式错误: {}, 使用默认值0", template.getItemKey());
            rankingConfig.setItemKey((byte) 0);
        }

        // 设置成员分榜
        rankingConfig.setMemberKey(template.getMemberKey());

        // 设置阶段分组编码
        rankingConfig.setPhaseGroupCode(phaseGroupCode);

        // 设置性能优化参数
        rankingConfig.setSkipRankUpdate(0);
        rankingConfig.setSkipPhaseUpdate(0);

        // 设置活动群组（默认为2）
        rankingConfig.setActGroup(2);

        // 设置榜单显示
        rankingConfig.setIsShow((byte) 1);

        // 设置榜单显示排序（默认为1）
        rankingConfig.setPosition(1);

        // 设置备注
        String remark = String.format("从榜单配置模板转换，原榜单ID: %s, 角色: %s, 礼物: %s",
            template.getRankId(), template.getRoles(), template.getItemIds());
        rankingConfig.setRemark(remark);

        // 设置扩展数据
        String extJson = String.format(
            "{\"templateRankId\":\"%s\",\"templateRankName\":\"%s\",\"roles\":\"%s\",\"itemIds\":\"%s\"}",
            template.getRankId(), template.getRankName(), template.getRoles(), template.getItemIds());
        rankingConfig.setExtjson(extJson);

        return rankingConfig;
    }

    /**
     * 解析日期时间字符串
     *
     * @param dateTimeStr 日期时间字符串
     * @return Date 对象
     */
    private Date parseDateTime(String dateTimeStr) throws ParseException {
        // 尝试多种日期格式
        String[] formats = {
            "yyyy-MM-dd HH:mm:ss",
            "yyyy-MM-dd",
            "yyyy/MM/dd HH:mm:ss",
            "yyyy/MM/dd"
        };

        for (String format : formats) {
            try {
                SimpleDateFormat sdf = new SimpleDateFormat(format);
                return sdf.parse(dateTimeStr.trim());
            } catch (ParseException e) {
                // 继续尝试下一种格式
            }
        }
        throw new ParseException("无法解析日期时间: " + dateTimeStr, 0);
    }
}
