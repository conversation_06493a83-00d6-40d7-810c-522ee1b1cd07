package com.yy.hdzt.service;

import com.yy.hdzt.bean.hdzt.RankingPhaseGroup;
import com.yy.hdzt.mapper.hdzt.RankingPhaseGroupMapper;
import com.yy.hdzt.template.ActScheduleTemplate;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import java.util.ArrayList;
import java.util.List;

/**
 * ActScheduleTemplate 转换为 RankingPhaseGroup 服务类
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class RankingPhaseGroupService {
    
    private final RankingPhaseGroupMapper rankingPhaseGroupMapper;
    

    public int insertActSchedulesToPhaseGroup(String dbName, Long actId, List<ActScheduleTemplate> actSchedules) {
        try {
            if (actSchedules == null || actSchedules.isEmpty()) {
                log.warn("ActScheduleTemplate 列表为空，无法插入");
                return 0;
            }
            
            // 转换 ActScheduleTemplate 为 RankingPhaseGroup
            List<RankingPhaseGroup> phaseGroups = convertToPhaseGroups(actId, actSchedules);
            if (phaseGroups.isEmpty()) {
                log.warn("转换后的 RankingPhaseGroup 列表为空");
                return 0;
            }
            
            // 批量插入
            int result = rankingPhaseGroupMapper.batchInsertPhaseGroups(dbName, phaseGroups);
            log.info("ActScheduleTemplate 列表插入完成，活动ID: {}, 成功数量: {}, 数据库: {}", 
                actId, result, dbName);
            
            return result;
            
        } catch (Exception e) {
            log.error("插入 ActScheduleTemplate 列表失败: {}", e.getMessage(), e);
            throw new RuntimeException("插入 ActScheduleTemplate 列表失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 将 ActScheduleTemplate 列表转换为 RankingPhaseGroup 列表
     * 
     * @param actId 活动ID
     * @param actSchedules ActScheduleTemplate 列表
     * @return RankingPhaseGroup 列表
     */
    private List<RankingPhaseGroup> convertToPhaseGroups(Long actId, List<ActScheduleTemplate> actSchedules) {
        List<RankingPhaseGroup> phaseGroups = new ArrayList<>();
        
        for (ActScheduleTemplate schedule : actSchedules) {
            try {
                RankingPhaseGroup phaseGroup = new RankingPhaseGroup();
                
                // 设置活动ID
                phaseGroup.setActId(actId);
                
                // 使用赛程编码作为分组编码
                phaseGroup.setPhaseGroupCode(schedule.getScheduleCode());
                
                // 使用赛程名称作为分组名称
                phaseGroup.setPhaseGroupName(schedule.getScheduleName());
                
                // 设置默认状态为有效
                phaseGroup.setStatus((byte) 1);
                
                // 设置备注信息
                phaseGroup.setRemark(String.format("从赛程模板转换，赛程ID: %s", schedule.getId()));
                
                // 设置扩展数据（可以包含原始的赛程信息）
                String extJson = String.format("{\"scheduleId\":\"%s\",\"scheduleCode\":\"%s\",\"scheduleName\":\"%s\"}", 
                    schedule.getId(), schedule.getScheduleCode(), schedule.getScheduleName());
                phaseGroup.setExtjson(extJson);
                
                phaseGroups.add(phaseGroup);
                
                log.info("转换赛程模板成功，赛程ID: {}, 赛程名称: {}, 分组编码: {}",
                    schedule.getId(), schedule.getScheduleName(), schedule.getScheduleCode());
                
            } catch (Exception e) {
                log.error("转换赛程模板失败，赛程ID: {}, 错误: {}", schedule.getId(), e.getMessage());
            }
        }
        return phaseGroups;
    }

}
