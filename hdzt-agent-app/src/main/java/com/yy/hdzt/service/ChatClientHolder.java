package com.yy.hdzt.service;

import com.yy.hdzt.vo.ModelEnum;
import lombok.RequiredArgsConstructor;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> 2025/6/12
 */
@Component
@RequiredArgsConstructor
public class ChatClientHolder implements ApplicationContextAware {

    private ApplicationContext applicationContext;

    public ChatClient getChatClient(ModelEnum model) {
        return switch (model) {
            case DeepSeek -> applicationContext.getBean("deepSeekChatClient", ChatClient.class );
            case OpenAi -> applicationContext.getBean("openAiChatClient", ChatClient.class);
            case MistralAi -> applicationContext.getBean("mistralAiChatClient", ChatClient.class);
            case DashScope -> applicationContext.getBean("dashScopeChatClient", ChatClient.class);
            case null -> applicationContext.getBean("openAiChatClient", ChatClient.class);
        };
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }
}
