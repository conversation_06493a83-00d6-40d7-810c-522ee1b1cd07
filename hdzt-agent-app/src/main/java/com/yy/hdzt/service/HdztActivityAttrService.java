package com.yy.hdzt.service;

import com.yy.hdzt.bean.hdzt.HdztActivityAttr;
import com.yy.hdzt.mapper.hdzt.HdztActivityAttrMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

@Component
public class HdztActivityAttrService {

    private static final String GRAY_STATUS_ATTR_NAME = "activity_grey_status";

    private static final String GRAY_STATUS_ATTR_VALUE = "0";

    @Resource
    private HdztActivityAttrMapper hdztActivityAttrMapper;

    public void insertGrayStatusAttr(String dbName, Long actId) {
        hdztActivityAttrMapper.insertAttr(dbName, new HdztActivityAttr() {{
            setActId(actId);
            setAttrname(GRAY_STATUS_ATTR_NAME);
            setAttrvalue(GRAY_STATUS_ATTR_VALUE);
        }});
    }
}
