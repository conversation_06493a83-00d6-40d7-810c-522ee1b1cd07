package com.yy.hdzt.service;

import com.yy.hdzt.mapper.hdzt.HdztActivityMapper;
import com.yy.hdzt.template.ActConfigTemplate;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

@Component
public class HdztActivityService {

    @Resource
    private HdztActivityMapper hdztActivityMapper;

    public void initAct(ActConfigTemplate actConfigTemplate) {
        hdztActivityMapper.insertFromTemplate(actConfigTemplate.getHdztDb(), actConfigTemplate);
    }
}
