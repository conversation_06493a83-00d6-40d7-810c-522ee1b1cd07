package com.yy.hdzt.service;

import com.yy.hdzt.bean.hdzt.RankingItemTransform;
import com.yy.hdzt.mapper.hdzt.RankingItemTransformMapper;
import com.yy.hdzt.template.ActGiftTemplate;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import java.util.ArrayList;
import java.util.List;

@Service
@Slf4j
public class RankingItemTransformService {

    @Resource
    private RankingItemTransformMapper rankingItemTransformMapper;

    /**
     * 将 ActGiftTemplate 列表插入到 ranking_config 表中
     *
     * @param dbName 数据库名称
     * @param actId 活动ID
     * @param actGifts ActGiftTemplate 列表
     * @return 成功插入的数量
     */
    public int insertActGiftsToRankingConfig(String dbName, Long actId, List<ActGiftTemplate> actGifts) {
        try {
            if (actGifts == null || actGifts.isEmpty()) {
                log.warn("ActGiftTemplate 列表为空，无法插入");
                return 0;
            }

            // 转换 ActGiftTemplate 为 RankingItemTransform
            List<RankingItemTransform> transforms = convertGiftsToTransforms(actId, actGifts);

            if (transforms.isEmpty()) {
                log.warn("转换后的 RankingItemTransform 列表为空");
                return 0;
            }

            // 批量插入
            int result = rankingItemTransformMapper.batchInsertTransforms(dbName, transforms);

            log.info("ActGiftTemplate 列表插入完成，活动ID: {}, 成功数量: {}, 数据库: {}",
                actId, result, dbName);

            return result;

        } catch (Exception e) {
            log.error("插入 ActGiftTemplate 列表失败: {}", e.getMessage(), e);
            throw new RuntimeException("插入 ActGiftTemplate 列表失败: " + e.getMessage(), e);
        }
    }

    /**
     * 将 ActGiftTemplate 列表转换为 RankingItemTransform 列表
     *
     * @param actId 活动ID
     * @param actGifts ActGiftTemplate 列表
     * @return RankingItemTransform 列表
     */
    private List<RankingItemTransform> convertGiftsToTransforms(Long actId, List<ActGiftTemplate> actGifts) {
        List<RankingItemTransform> transforms = new ArrayList<>();

        for (int i = 0; i < actGifts.size(); i++) {
            ActGiftTemplate gift = actGifts.get(i);
            try {
                RankingItemTransform transform = convertGiftToTransform(actId, gift, i + 1);
                transforms.add(transform);

                log.debug("转换礼物模板成功，礼物ID: {}, 礼物名称: {}",
                    gift.getHdztItemId(), gift.getItemName());

            } catch (Exception e) {
                log.error("转换礼物模板失败，礼物ID: {}, 错误: {}", gift.getHdztItemId(), e.getMessage());
                // 继续处理其他礼物，不中断整个流程
            }
        }

        return transforms;
    }

    /**
     * 将单个 ActGiftTemplate 转换为 RankingItemTransform
     *
     * @param actId 活动ID
     * @param gift ActGiftTemplate
     * @param position 排序位置（暂未使用，保留用于扩展）
     * @return RankingItemTransform
     */
    private RankingItemTransform convertGiftToTransform(Long actId, ActGiftTemplate gift, int position) {
        RankingItemTransform transform = new RankingItemTransform();

        // 设置活动ID
        transform.setActId(actId.intValue());

        // 设置业务ID（可以从活动配置中获取，这里暂时设置为默认值）
        // TODO: 从活动配置中获取正确的业务ID
        transform.setBusiId(810); // 默认业务ID，聊天室/语音房

        // 设置业务项目标识
        transform.setBusiItemId(gift.getBusiItemId());

        // 设置中台项目标识
        transform.setHdztItemId(gift.getHdztItemId());

        // 设置项目名称
        transform.setItemName(gift.getItemName());

        // 设置项目图片URL（暂时为空，可以后续扩展）
        transform.setItemUrl(null);

        // 设置礼物积分
        try {
            // 尝试将字符串转换为Long类型
            Long itemScore = Long.parseLong(gift.getItemScore());
            transform.setItemScore(itemScore);
        } catch (NumberFormatException e) {
            log.warn("礼物积分不是有效数字: {}, 设置为0", gift.getItemScore());
            transform.setItemScore(0L);
        }

        // 设置备注
        transform.setRemark(String.format("从礼物模板转换，位置: %d, 礼物ID: %s, 积分: %s",
            position, gift.getHdztItemId(), gift.getItemScore()));

        // 设置扩展数据
        String extJson = String.format("{\"position\":%d,\"hdztItemId\":\"%s\",\"busiItemId\":\"%s\",\"itemScore\":\"%s\",\"remark\":\"%s\"}",
            position, gift.getHdztItemId(), gift.getBusiItemId(), gift.getItemScore(),
            gift.getRemark() != null ? gift.getRemark() : "");
        transform.setExtjson(extJson);

        return transform;
    }
}
