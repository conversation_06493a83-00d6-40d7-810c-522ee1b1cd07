package com.yy.hdzt.enums;

/**
 * 数据库名称枚举
 */
public enum DatabaseEnum {
    
    HDZT("hdzt", "主数据库"),
    HDZT2_WEST("hdzt2_west", "西区数据库"),
    HDZT3_SOUTH("hdzt3_south", "南区数据库"),
    HDZT4_NORTH("hdzt4_north", "北区数据库"),
    HDZT5_MIDDLE("hdzt5_middle", "中区数据库"),
    HDZT6_UP("hdzt6_up", "上区数据库"),
    HDZT7_DOWN("hdzt7_down", "下区数据库"),
    
    HDZK("hdzk", "主控数据库"),
    HDZK2_WEST("hdzk2_west", "西区主控数据库"),
    HDZK3_SOUTH("hdzk3_south", "南区主控数据库"),
    HDZK4_NORTH("hdzk4_north", "北区主控数据库"),
    HDZK5_MIDDLE("hdzk5_middle", "中区主控数据库"),
    HDZK6_UP("hdzk6_up", "上区主控数据库"),
    HDZK7_DOWN("hdzk7_down", "下区主控数据库");
    
    private final String dbName;
    private final String description;
    
    DatabaseEnum(String dbName, String description) {
        this.dbName = dbName;
        this.description = description;
    }
    
    public String getDbName() {
        return dbName;
    }
    
    public String getDescription() {
        return description;
    }
    
    /**
     * 根据数据库名称获取枚举
     */
    public static DatabaseEnum getByDbName(String dbName) {
        for (DatabaseEnum db : values()) {
            if (db.getDbName().equals(dbName)) {
                return db;
            }
        }
        return HDZT; // 默认返回主数据库
    }
}
