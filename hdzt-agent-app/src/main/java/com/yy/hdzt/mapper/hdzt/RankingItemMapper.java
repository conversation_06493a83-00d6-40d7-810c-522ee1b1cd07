package com.yy.hdzt.mapper.hdzt;

import com.yy.hdzt.bean.hdzt.RankingItem;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 计榜项目表 Mapper 接口
 */
@Mapper
public interface RankingItemMapper {

    /**
     * 根据活动ID删除计榜项目（动态数据库）
     *
     * @param dbName 数据库名称
     * @param actId 活动标识
     * @return 影响行数
     */
    @Delete("""
            <script>
               DELETE FROM ${dbName}.ranking_item
               WHERE act_id = #{actId}
            </script>
            """)
    int deleteByActId(@Param("dbName") String dbName, @Param("actId") Long actId);
}
