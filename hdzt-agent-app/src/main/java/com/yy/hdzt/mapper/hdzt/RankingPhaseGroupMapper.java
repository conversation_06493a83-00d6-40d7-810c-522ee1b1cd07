package com.yy.hdzt.mapper.hdzt;

import com.yy.hdzt.bean.hdzt.RankingPhaseGroup;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 阶段分组表 Mapper 接口
 */
@Mapper
public interface RankingPhaseGroupMapper {

    /**
     * 插入阶段分组信息（动态数据库）
     *
     * @param dbName 数据库名称
     * @param phaseGroup 阶段分组信息
     * @return 影响行数
     */
    @Insert("""
            <script>
               INSERT INTO ${dbName}.ranking_phase_group (
                   act_id, phase_group_code, phase_group_name, status,
                   remark, extjson, ctime, utime
               ) VALUES (
                   #{group.actId}, #{group.phaseGroupCode}, #{group.phaseGroupName},
                   #{group.status}, #{group.remark}, #{group.extjson},
                   NOW(), NOW()
               )
            </script>
            """)
    int insertPhaseGroup(@Param("dbName") String dbName, @Param("group") RankingPhaseGroup phaseGroup);

    /**
     * 批量插入阶段分组列表（动态数据库）
     *
     * @param dbName 数据库名称
     * @param phaseGroupList RankingPhaseGroup 列表
     * @return 影响行数
     */
    @Insert("""
            <script>
               INSERT INTO ${dbName}.ranking_phase_group (
                   act_id, phase_group_code, phase_group_name, status,
                   remark, extjson, ctime, utime
               ) VALUES
               <foreach collection="phaseGroupList" item="group" separator=",">
                   (
                       #{group.actId}, #{group.phaseGroupCode}, #{group.phaseGroupName},
                       #{group.status}, #{group.remark}, #{group.extjson},
                       NOW(), NOW()
                   )
               </foreach>
            </script>
            """)
    int batchInsertPhaseGroups(@Param("dbName") String dbName,
                              @Param("phaseGroupList") List<RankingPhaseGroup> phaseGroupList);

    /**
     * 根据活动ID删除阶段分组（动态数据库）
     *
     * @param dbName 数据库名称
     * @param actId 活动标识
     * @return 影响行数
     */
    @Delete("""
            <script>
               DELETE FROM ${dbName}.ranking_phase_group
               WHERE act_id = #{actId}
            </script>
            """)
    int deleteByActId(@Param("dbName") String dbName, @Param("actId") Long actId);

}
