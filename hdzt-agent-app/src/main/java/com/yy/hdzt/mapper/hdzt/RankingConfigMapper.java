package com.yy.hdzt.mapper.hdzt;

import com.yy.hdzt.bean.hdzt.RankingConfig;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 榜单配置表 Mapper 接口
 */
@Mapper
public interface RankingConfigMapper {

    /**
     * 批量插入榜单配置列表（动态数据库）
     *
     * @param dbName 数据库名称
     * @param configList RankingConfig 列表
     * @return 影响行数
     */
    @Insert("""
            <script>
               INSERT INTO ${dbName}.ranking_config (
                   act_id, rank_id, rank_name, rank_name_show, rank_type, status,
                   calc_begin_time, calc_end_time, value_type, limit_item, time_key,
                   time_key_begin, time_key_end, item_key, member_key, phase_group_code,
                   skip_rank_update, skip_phase_update, act_group, is_show,
                   show_begin_time, show_end_time, position, remark, extjson,
                   ctime, utime
               ) VALUES
               <foreach collection="configList" item="config" separator=",">
                   (
                       #{config.actId}, #{config.rankId}, #{config.rankName},
                       #{config.rankNameShow}, #{config.rankType}, #{config.status},
                       #{config.calcBeginTime}, #{config.calcEndTime}, #{config.valueType},
                       #{config.limitItem}, #{config.timeKey}, #{config.timeKeyBegin},
                       #{config.timeKeyEnd}, #{config.itemKey}, #{config.memberKey},
                       #{config.phaseGroupCode}, #{config.skipRankUpdate}, #{config.skipPhaseUpdate},
                       #{config.actGroup}, #{config.isShow}, #{config.showBeginTime},
                       #{config.showEndTime}, #{config.position}, #{config.remark},
                       #{config.extjson}, NOW(), NOW()
                   )
               </foreach>
            </script>
            """)
    int batchInsertRankingConfigs(@Param("dbName") String dbName,
                                 @Param("configList") List<RankingConfig> configList);

    /**
     * 根据活动ID删除榜单配置（动态数据库）
     *
     * @param dbName 数据库名称
     * @param actId 活动标识
     * @return 影响行数
     */
    @Delete("""
            <script>
               DELETE FROM ${dbName}.ranking_config
               WHERE act_id = #{actId}
            </script>
            """)
    int deleteByActId(@Param("dbName") String dbName, @Param("actId") Long actId);

}
