package com.yy.hdzt.mapper.hdzt;

import com.yy.hdzt.bean.hdzt.HdztEnrollPolicy;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 参赛报名策略表 Mapper 接口
 */
@Mapper
public interface HdztEnrollPolicyMapper {
    /**
     * 批量插入报名策略列表（动态数据库）
     *
     * @param dbName 数据库名称
     * @param policyList HdztEnrollPolicy 列表
     * @return 影响行数
     */
    @Insert("""
            <script>
               INSERT INTO ${dbName}.hdzt_enroll_policy (
                   act_id, role_id, check_flag, convert_flag, pass_way,
                   auto_enroll, extjson, remark
               ) VALUES
               <foreach collection="policyList" item="policy" separator=",">
                   (
                       #{policy.actId}, #{policy.roleId}, #{policy.checkFlag},
                       #{policy.convertFlag}, #{policy.passWay}, #{policy.autoEnroll},
                       #{policy.extjson}, #{policy.remark}
                   )
               </foreach>
            </script>
            """)
    int batchInsertEnrollPolicies(@Param("dbName") String dbName,
                                 @Param("policyList") List<HdztEnrollPolicy> policyList);

    /**
     * 根据活动ID删除报名策略（动态数据库）
     *
     * @param dbName 数据库名称
     * @param actId 活动标识
     * @return 影响行数
     */
    @Delete("""
            <script>
               DELETE FROM ${dbName}.hdzt_enroll_policy
               WHERE act_id = #{actId}
            </script>
            """)
    int deleteByActId(@Param("dbName") String dbName, @Param("actId") Long actId);

}
