package com.yy.hdzt.mapper.hdzt;

import com.yy.hdzt.bean.hdzt.HdztActivity;
import com.yy.hdzt.template.ActConfigTemplate;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 活动信息表 Mapper 接口
 */
@Mapper
public interface HdztActivityMapper {


    /**
     * 根据活动ID查询活动信息（动态数据库）
     *
     * @param dbName 数据库名称 (如: hdzt, hdzt2_west, hdzt3_south 等)
     * @param actId 活动标识
     * @return 活动信息
     */
    @Select("""
            <script>
               SELECT *
               FROM ${dbName}.hdzt_activity
               WHERE act_id = #{actId}
            </script>
            """)
    HdztActivity selectByActId(@Param("dbName") String dbName, @Param("actId") Integer actId);

    /**
     * 将 ActConfigTemplate 插入到 hdzt_activity 表中（动态数据库）
     *
     * @param dbName 数据库名称
     * @param template 活动配置模板
     * @return 影响行数
     */
    @Insert("""
            <script>
               INSERT INTO ${dbName}.hdzt_activity (
                   act_id, busi_id, act_name, status, begin_time, end_time,
                   begin_time_show, end_time_show, act_type, act_group,
                   act_group_archive, secret, act_bg_url, detail_url,
                   remark, extjson, ctime, utime
               ) VALUES (
                   #{template.actId}, #{template.busiId}, #{template.actName},
                   1,
                   #{template.beginTime},
                   #{template.endTime},
                   #{template.beginTime},
                   #{template.endTime},
                   1,
                   #{template.actGroup},
                   101, '', NULL, NULL,
                   '', NULL, NOW(), NOW()
               )
            </script>
            """)
    int insertFromTemplate(@Param("dbName") String dbName,
                          @Param("template") ActConfigTemplate template);

    /**
     * 根据活动ID删除活动信息（动态数据库）
     *
     * @param dbName 数据库名称 (如: hdzt, hdzt2_west, hdzt3_south 等)
     * @param actId 活动标识
     * @return 影响行数
     */
    @Delete("""
            <script>
               DELETE FROM ${dbName}.hdzt_activity
               WHERE act_id = #{actId}
            </script>
            """)
    int deleteByActId(@Param("dbName") String dbName, @Param("actId") Long actId);

}
