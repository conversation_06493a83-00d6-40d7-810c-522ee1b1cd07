package com.yy.hdzt.mapper.hdzt;

import com.yy.hdzt.bean.hdzt.RankingItemTransform;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 项目标识转换表 Mapper 接口
 */
@Mapper
public interface RankingItemTransformMapper {

    /**
     * 批量插入项目标识转换列表（动态数据库）
     *
     * @param dbName 数据库名称
     * @param transformList RankingItemTransform 列表
     * @return 影响行数
     */
    @Insert("""
            <script>
               INSERT INTO ${dbName}.ranking_item_transform (
                   act_id, busi_id, busi_item_id, hdzt_item_id, item_name, item_url,
                   item_score, remark, extjson, ctime, utime
               ) VALUES
               <foreach collection="transformList" item="transform" separator=",">
                   (
                       #{transform.actId}, #{transform.busiId}, #{transform.busiItemId},
                       #{transform.hdztItemId}, #{transform.itemName}, #{transform.itemUrl},
                       #{transform.itemScore}, #{transform.remark}, #{transform.extjson},
                       NOW(), NOW()
                   )
               </foreach>
            </script>
            """)
    int batchInsertTransforms(@Param("dbName") String dbName,
                             @Param("transformList") List<RankingItemTransform> transformList);

    /**
     * 根据活动ID删除项目标识转换（动态数据库）
     *
     * @param dbName 数据库名称
     * @param actId 活动标识
     * @return 影响行数
     */
    @Delete("""
            <script>
               DELETE FROM ${dbName}.ranking_item_transform
               WHERE act_id = #{actId}
            </script>
            """)
    int deleteByActId(@Param("dbName") String dbName, @Param("actId") Long actId);
}
