package com.yy.hdzt.mapper.hdzk;

import com.yy.hdzt.bean.hdzk.HdzjComponentAttr;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 组件属性表 Mapper 接口
 * 对应表: hdzj_component_attr
 */
@Mapper
public interface HdzjComponentAttrMapper {

    /**
     * 批量插入组件属性（动态数据库）
     *
     * @param dbName 数据库名称
     * @param attrs 组件属性信息列表
     * @return 影响行数
     */
    @Insert("""
            <script>
               INSERT INTO ${dbName}.hdzj_component_attr (
                   act_id, cmpt_id, cmpt_use_inx, name, value, remark, ctime, utime
               ) VALUES
               <foreach collection="attrs" item="attr" separator=",">
                   (#{attr.actId}, #{attr.cmptId}, #{attr.cmptUseInx}, #{attr.name},
                    #{attr.value}, #{attr.remark}, NOW(), NOW())
               </foreach>
            </script>
            """)
    int batchInsertAttrs(@Param("dbName") String dbName, @Param("attrs") List<HdzjComponentAttr> attrs);

    /**
     * 根据活动ID删除组件属性（动态数据库）
     *
     * @param dbName 数据库名称
     * @param actId 活动标识
     * @return 影响行数
     */
    @Delete("""
            <script>
               DELETE FROM ${dbName}.hdzj_component_attr
               WHERE act_id = #{actId}
            </script>
            """)
    int deleteByActId(@Param("dbName") String dbName, @Param("actId") Long actId);
}
