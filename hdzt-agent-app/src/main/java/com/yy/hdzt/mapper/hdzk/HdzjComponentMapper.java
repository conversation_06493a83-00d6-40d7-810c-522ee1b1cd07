package com.yy.hdzt.mapper.hdzk;

import com.yy.hdzt.bean.hdzk.HdzjComponent;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * 组件表 Mapper 接口
 * 对应表: hdzj_component
 */
@Mapper
public interface HdzjComponentMapper {

    /**
     * 批量插入组件（动态数据库）
     *
     * @param dbName            数据库名称
     * @param hdzjComponentList 组件信息列表
     * @return 影响行数
     */
    @Insert("""
            <script>
               INSERT INTO ${dbName}.hdzj_component (
                   act_id, cmpt_id, cmpt_use_inx, module_name, status, cmpt_title, extjson, show_order, remark, ctime, utime
               ) VALUES
               <foreach collection="hdzjComponentList" item="item" separator=",">
                   (#{item.actId}, #{item.cmptId}, #{item.cmptUseInx}, #{item.moduleName}, #{item.status},
                    #{item.cmptTitle}, #{item.extjson}, #{item.showOrder}, #{item.remark}, NOW(), NOW())
               </foreach>
            </script>
            """)
    int batchInsertHdzjComponent(@Param("dbName") String dbName, @Param("hdzjComponentList") List<HdzjComponent> hdzjComponentList);

    /**
     * 根据活动ID删除组件（动态数据库）
     *
     * @param dbName 数据库名称
     * @param actId  活动标识
     * @return 影响行数
     */
    @Delete("""
            <script>
               DELETE FROM ${dbName}.hdzj_component
               WHERE act_id = #{actId}
            </script>
            """)
    int deleteByActId(@Param("dbName") String dbName, @Param("actId") Long actId);
}