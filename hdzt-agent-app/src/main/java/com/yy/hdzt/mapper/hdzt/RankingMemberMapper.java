package com.yy.hdzt.mapper.hdzt;

import com.yy.hdzt.bean.hdzt.RankingMember;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 计榜成员表 Mapper 接口
 * 为指定的参与者集合创建榜单（按顺序形成 member）
 */
@Mapper
public interface RankingMemberMapper {

    /**
     * 批量插入计榜成员列表（动态数据库）
     *
     * @param dbName 数据库名称
     * @param memberList RankingMember 列表
     * @return 影响行数
     */
    @Insert("""
            <script>
               INSERT INTO ${dbName}.ranking_member (
                   act_id, rank_id, roles, remark, extjson, ctime, utime
               ) VALUES
               <foreach collection="memberList" item="member" separator=",">
                   (
                       #{member.actId}, #{member.rankId}, #{member.roles},
                       #{member.remark}, #{member.extjson}, NOW(), NOW()
                   )
               </foreach>
            </script>
            """)
    int batchInsertRankingMembers(@Param("dbName") String dbName,
                                 @Param("memberList") List<RankingMember> memberList);

    /**
     * 根据活动ID删除计榜成员（动态数据库）
     *
     * @param dbName 数据库名称
     * @param actId 活动标识
     * @return 影响行数
     */
    @Delete("""
            <script>
               DELETE FROM ${dbName}.ranking_member
               WHERE act_id = #{actId}
            </script>
            """)
    int deleteByActId(@Param("dbName") String dbName, @Param("actId") Long actId);

}
