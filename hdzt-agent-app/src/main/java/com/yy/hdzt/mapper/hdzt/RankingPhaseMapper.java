package com.yy.hdzt.mapper.hdzt;

import com.yy.hdzt.bean.hdzt.RankingPhase;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 排行榜阶段表 Mapper 接口
 */
@Mapper
public interface RankingPhaseMapper {

    /**
     * 插入排行榜阶段信息（动态数据库）
     *
     * @param dbName 数据库名称
     * @param phase 排行榜阶段信息
     * @return 影响行数
     */
    @Insert("""
            <script>
               INSERT INTO ${dbName}.ranking_phase (
                   act_id, phase_id, phase_name, phase_name_show, phase_group_code,
                   status, phase_bg_url, begin_time, end_time, show_begin_time,
                   show_end_time, task_value_type, recycle_count, recycle_type,
                   remark, extjson, ctime, utime
               ) VALUES (
                   #{phase.actId}, #{phase.phaseId}, #{phase.phaseName},
                   #{phase.phaseNameShow}, #{phase.phaseGroupCode}, #{phase.status},
                   #{phase.phaseBgUrl}, #{phase.beginTime}, #{phase.endTime},
                   #{phase.showBeginTime}, #{phase.showEndTime}, #{phase.taskValueType},
                   #{phase.recycleCount}, #{phase.recycleType}, #{phase.remark},
                   #{phase.extjson}, NOW(), NOW()
               )
            </script>
            """)
    int insertPhase(@Param("dbName") String dbName, @Param("phase") RankingPhase phase);

    /**
     * 批量插入排行榜阶段列表（动态数据库）
     *
     * @param dbName 数据库名称
     * @param phaseList RankingPhase 列表
     * @return 影响行数
     */
    @Insert("""
            <script>
               INSERT INTO ${dbName}.ranking_phase (
                   act_id, phase_id, phase_name, phase_name_show, phase_group_code,
                   status, phase_bg_url, begin_time, end_time, show_begin_time,
                   show_end_time, task_value_type, recycle_count, recycle_type,
                   remark, extjson, ctime, utime
               ) VALUES
               <foreach collection="phaseList" item="phase" separator=",">
                   (
                       #{phase.actId}, #{phase.phaseId}, #{phase.phaseName},
                       #{phase.phaseNameShow}, #{phase.phaseGroupCode}, #{phase.status},
                       #{phase.phaseBgUrl}, #{phase.beginTime}, #{phase.endTime},
                       #{phase.showBeginTime}, #{phase.showEndTime}, #{phase.taskValueType},
                       #{phase.recycleCount}, #{phase.recycleType}, #{phase.remark},
                       #{phase.extjson}, NOW(), NOW()
                   )
               </foreach>
            </script>
            """)
    int batchInsertPhases(@Param("dbName") String dbName,
                         @Param("phaseList") List<RankingPhase> phaseList);

    /**
     * 根据活动ID删除排行榜阶段（动态数据库）
     *
     * @param dbName 数据库名称
     * @param actId 活动标识
     * @return 影响行数
     */
    @Delete("""
            <script>
               DELETE FROM ${dbName}.ranking_phase
               WHERE act_id = #{actId}
            </script>
            """)
    int deleteByActId(@Param("dbName") String dbName, @Param("actId") Long actId);

}
