package com.yy.hdzt.mapper.hdzt;

import com.yy.hdzt.bean.hdzt.HdztActivityAttr;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 活动信息属性表 Mapper 接口
 */
@Mapper
public interface HdztActivityAttrMapper {

    /**
     * 插入活动属性信息（动态数据库）
     *
     * @param dbName 数据库名称
     * @param attr 活动属性信息
     * @return 影响行数
     */
    @Insert("""
            <script>
               INSERT INTO ${dbName}.hdzt_activity_attr (
                   act_id, attrname, attrvalue, remark, ctime, utime
               ) VALUES (
                   #{attr.actId}, #{attr.attrname}, #{attr.attrvalue},
                   #{attr.remark}, NOW(), NOW()
               )
            </script>
            """)
    int insertAttr(@Param("dbName") String dbName, @Param("attr") HdztActivityAttr attr);

    /**
     * 根据活动ID删除活动属性信息（动态数据库）
     *
     * @param dbName 数据库名称
     * @param actId 活动标识
     * @return 影响行数
     */
    @Delete("""
            <script>
               DELETE FROM ${dbName}.hdzt_activity_attr
               WHERE act_id = #{actId}
            </script>
            """)
    int deleteByActId(@Param("dbName") String dbName, @Param("actId") Long actId);
}
