package com.yy.hdzt.mapper.hdzk;

import com.yy.hdzt.bean.hdzk.HdzjComponentDefine;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

public interface HdzjComponentDefineMapper {

    @Select("""
        SELECT cmpt_id, cmpt_title, state, author, remark, ctime, utime, autotest_task_id, autotest_creator, use_mysql
        FROM ${dbName}.hdzj_component_define
        WHERE state = #{state}
    """)
    List<HdzjComponentDefine> selectComponentsByState(@Param("dbName") String dbName,@Param("state") int state);
}
