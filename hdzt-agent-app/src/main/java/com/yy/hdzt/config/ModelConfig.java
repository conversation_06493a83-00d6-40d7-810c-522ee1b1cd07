package com.yy.hdzt.config;

import com.alibaba.cloud.ai.dashscope.chat.DashScopeChatModel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.client.ChatClientRequest;
import org.springframework.ai.chat.client.ChatClientResponse;
import org.springframework.ai.chat.client.advisor.api.Advisor;
import org.springframework.ai.chat.client.advisor.api.CallAdvisor;
import org.springframework.ai.chat.client.advisor.api.CallAdvisorChain;
import org.springframework.ai.mistralai.MistralAiChatModel;
import org.springframework.ai.model.ModelOptionsUtils;
import org.springframework.ai.openai.OpenAiChatModel;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * <AUTHOR> 2025/5/28
 */
@Configuration
public class ModelConfig {

    @Bean
    public ChatClient mistralAiChatClient(MistralAiChatModel chatModel, List<Advisor> advisors) {
        return ChatClient.builder(chatModel)
                .defaultAdvisors(advisors)
                .build();
    }

    @Bean
    public ChatClient openAiChatClient(OpenAiChatModel openAiChatModel, List<Advisor> advisors) {
        return ChatClient.builder(openAiChatModel)
                .defaultAdvisors(advisors)
                .build();
    }

    @Bean
    public ChatClient dashScopeChatClient(DashScopeChatModel dashScopeChatModel, List<Advisor> advisors) {
        return ChatClient.builder(dashScopeChatModel)
                .defaultAdvisors(advisors)
                .build();
    }

    @Bean
    public Advisor loggerAdvisor() {
        return new LoggerAdvisor();
    }

    @Slf4j
    public static class LoggerAdvisor implements CallAdvisor {

        @Override
        public ChatClientResponse adviseCall(ChatClientRequest chatClientRequest, CallAdvisorChain callAdvisorChain) {
            log.info("request: {}", ModelOptionsUtils.toJsonStringPrettyPrinter(chatClientRequest));
            ChatClientResponse chatClientResponse = callAdvisorChain.nextCall(chatClientRequest);
            log.info("response: {}", ModelOptionsUtils.toJsonStringPrettyPrinter(chatClientResponse));
            return chatClientResponse;
        }

        @Override
        public String getName() {
            return "LoggerAdvisor";
        }

        @Override
        public int getOrder() {
            return 0;
        }
    }

}
