package com.yy.hdzt.bean.hdzt;

import lombok.Data;
import java.util.Date;

/**
 * 榜单配置表实体类
 */
@Data
public class RankingConfig {
    /**
     * 活动标识
     */
    private Integer actId;

    /**
     * 榜单标识
     */
    private Integer rankId;

    /**
     * 榜单显示的名字
     */
    private String rankName;

    /**
     * 榜单展示的名字
     */
    private String rankNameShow;

    /**
     * 榜单类型，0：通用类型， n：其它值（有的有标准实现，无标准实现的要用户自己实现）
     */
    private Integer rankType;

    /**
     * 榜单状态, 1-有效， 其它值-无效
     */
    private Byte status;

    /**
     * 计榜开始时间
     */
    private Date calcBeginTime;

    /**
     * 计榜结束时间
     */
    private Date calcEndTime;

    /**
     * 计值类型, 1：客观的数量,  2：换算的积分
     */
    private Integer valueType;

    /**
     * 项目限定 - 积分榜时（value_type=2）1-限制为登记项，非1-不限制计榜项目
     */
    private Integer limitItem;

    /**
     * 时间分榜: 0-不按时间再分, 1-按日再分，2-按小时再分
     */
    private Byte timeKey;

    /**
     * 时间分榜累计开始时间, time_flag=1,2 时为 HH:mm:ss, 比如 07:08:09
     */
    private String timeKeyBegin;

    /**
     * 时间分榜累计结束时间, time_flag=1,2 时为 HH:mm:ss, 比如 12:34:56
     */
    private String timeKeyEnd;

    /**
     * 项目分榜: 1-按项目再分，0-不按项目分了
     */
    private Byte itemKey;

    /**
     * 成员分榜, 格式为 role1|role2|role3...., 比如 4001|4002|4003， 对应的角色值组合如  /A/B/C 会作为榜单key的一部分
     */
    private String memberKey;

    /**
     * 若存在阶段分组，则进行阶段累榜和过任务
     */
    private String phaseGroupCode;

    /**
     * 跳过榜单更新（redis性能优化用），0：不跳过（缺省），非0：跳过
     */
    private Integer skipRankUpdate;

    /**
     * 跳过阶段更新（redis性能优化用），0：不跳过（缺省），非0：跳过
     */
    private Integer skipPhaseUpdate;

    /**
     * 活动群组(如果为0则以活动表为准) - 性能优化用，相同群组榜放同一redis实例，确保lua、pipeline 等完整执行，目前只能为2/3/4（1系统专用，具体活动请勿使用！）
     */
    private Integer actGroup;

    /**
     * 榜单是否显示
     */
    private Byte isShow;

    /**
     * 展示开始时间
     */
    private Date showBeginTime;

    /**
     * 展示结束时间
     */
    private Date showEndTime;

    /**
     * 榜单显示排序
     */
    private Integer position;

    /**
     * 榜单说明
     */
    private String remark;

    /**
     * 扩展数据
     */
    private String extjson;

    /**
     * 创建时间
     */
    private Date ctime;

    /**
     * 修改时间
     */
    private Date utime;
}
