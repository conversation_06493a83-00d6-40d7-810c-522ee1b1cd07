## 概述
HDZKJSON 用于为活动组件提供 JSON 格式的配置



## 文件格式
文件采用 JSON 格式，键为 `actId|cmptId|cmptUseInx` 的组合，值为组件属性的 JSON 对象。

```json
{
  "actId|cmptId|cmptUseInx": {
    "属性名1": "属性值1",
    "属性名2": "属性值2",
    ...
  }
}
``
```json
{
              "2025073001|1016|810": {
                "actId": 2025073001,
                "cmptId": 1016,
                "cmptUseInx": 810,
                "busiId": 400,
                "giftIds": ["20001", "20002"],
                "apps": ["yy"],
                "giftIcons": {
                  "20001": "gift_icon_20001",
                  "20002": "gift_icon_20002"
                },
                "tAwardPkgInfos": {
                  "2001": {
                    "medalPic": "medal_primary_pic_2",
                    "medalName": "新手勋章",
                    "qrCode": "qr_code_primary_2"
                  },
                  "2002": {
                    "medalPic": "medal_senior_pic_2",
                    "medalName": "进阶勋章",
                    "qrCode": "qr_code_senior_2"
                  }
                },
                "tAwardTskMedalId": 2002,
                "tAwardPkgPrimaryId": 2001,
                "tAwardPkgSeniorId": 2002,
                "tAwardPkgExpertId": 0,
                "expertRankId": 0,
                "expertPhaseId": 0,
                "threshold": 50000
              }
            }
``
CREATE TABLE `hdzj_component_attr` (
  `act_id` int(11) NOT NULL COMMENT '活动标识',
  `cmpt_id` int(11) NOT NULL COMMENT '组件标识',
  `cmpt_use_inx` int(11) NOT NULL COMMENT '组件使用序号',
  `name` varchar(128) NOT NULL COMMENT '属性名字',
  `value` varchar(9600) NOT NULL COMMENT '属性取值',
  `remark` varchar(1024) DEFAULT NULL COMMENT '属性说明',
  `ctime` datetime NOT NULL COMMENT '创建时间',
  `utime` datetime NOT NULL COMMENT '修改时间',
  PRIMARY KEY (`act_id`,`cmpt_id`,`cmpt_use_inx`,`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='组件属性'