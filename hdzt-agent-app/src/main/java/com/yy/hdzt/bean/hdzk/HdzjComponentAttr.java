package com.yy.hdzt.bean.hdzk;

import lombok.Data;
import java.util.Date;

/**
 * 组件属性表实体类
 * 对应表: hdzj_component_attr
 */
@Data
public class HdzjComponentAttr {
    
    /**
     * 活动标识
     */
    private Long actId;

    /**
     * 组件标识
     */
    private Long cmptId;

    /**
     * 组件使用序号
     */
    private Long cmptUseInx;

    /**
     * 属性名字
     */
    private String name;

    /**
     * 属性取值
     */
    private String value;

    /**
     * 属性说明
     */
    private String remark;

    /**
     * 数据库名称（根据活动ID套数确定，不插入数据库）
     */
    private String hdzkdb;

    /**
     * 创建时间
     */
    private Date ctime;

    /**
     * 修改时间
     */
    private Date utime;
}
