package com.yy.hdzt.bean.hdzt;

import lombok.Data;
import java.util.Date;

/**
 * 阶段分组表实体类
 * 一个活动可以安排不同阶段组合，榜单和阶段按需组合
 */
@Data
public class RankingPhaseGroup {
    /**
     * 活动标识
     */
    private Long actId;

    /**
     * 分组编码
     */
    private String phaseGroupCode;

    /**
     * 分组名称
     */
    private String phaseGroupName;

    /**
     * 分组状态, 1-有效， 其它值-无效
     */
    private Byte status;

    /**
     * 分组说明
     */
    private String remark;

    /**
     * 扩展数据
     */
    private String extjson;

    /**
     * 创建时间
     */
    private Date ctime;

    /**
     * 修改时间
     */
    private Date utime;
}
