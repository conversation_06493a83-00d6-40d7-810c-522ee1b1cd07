package com.yy.hdzt.bean.hdzt;

import lombok.Data;
import java.util.Date;

/**
 * 计榜成员表实体类
 * 为指定的参与者集合创建榜单（按顺序形成 member）
 */
@Data
public class RankingMember {
    /**
     * 活动标识
     */
    private Integer actId;

    /**
     * 榜单标识
     */
    private Integer rankId;

    /**
     * 参与者的身份或角色，用&标识与，|表示或，比如 101&201|308&309
     */
    private String roles;

    /**
     * 参与说明
     */
    private String remark;

    /**
     * 扩展数据
     */
    private String extjson;

    /**
     * 创建时间
     */
    private Date ctime;

    /**
     * 修改时间
     */
    private Date utime;
}
