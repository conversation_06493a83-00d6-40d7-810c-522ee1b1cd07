/*
 * @(#)HdzjComponent.java 2021-04-13
 *
 * Copy Right@ 欢聚时代
 *
 * 代码生成: hdzj_component 表的数据模型类  HdzjComponent
 */

package com.yy.hdzt.bean.hdzk;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;


@Data
public class HdzjComponent implements   Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 活动标识
     */
    private Long actId;

    /**
     * 组件标识
     */
    private Long cmptId;

    /**
     * 组件使用序号（取值：1，2，3....)
     */
    private Long cmptUseInx;

    /**
     * 模块名称，指的是实际用到哪个功能模块，如 金银宝箱
     */
    private String moduleName;

    /**
     * 组件状态， 0-无效，1-有效
     */
    private Integer status;

    /**
     * 组件标题
     */
    private String cmptTitle;

    /**
     * 扩展信息
     */
    private String extjson;

    /**
     * 显示排序
     */
    private Integer showOrder;

    /**
     * 组件说明
     */
    private String remark;

    /**
     * 创建时间
     */
    private Date ctime;

    /**
     * 修改时间
     */
    private Date utime;

    /**
     * 数据库名称（根据活动ID套数确定，不插入数据库）
     */
    private String hdzkdb;

}
