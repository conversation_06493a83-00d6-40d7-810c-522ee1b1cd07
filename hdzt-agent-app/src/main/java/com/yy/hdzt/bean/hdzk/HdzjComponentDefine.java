package com.yy.hdzt.bean.hdzk;

import lombok.Data;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.Serializable;
import java.util.Date;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2025-07-29 16:14
 **/
@Data
public class HdzjComponentDefine implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 组件id
     */
    private Long cmptId;
    /**
     * 组件标题
     */
    private String cmptTitle;

    /**
     * 1==使用中
     */
    private Integer state;

    /**
     * 作者-方便咨询
     */
    private String author;

    /**
     * 组件说明
     */
    private String remark;

    /**
     * 创建时间
     */
    private Date ctime;

    /**
     * 修改时间
     */
    private Date utime;

    /**
     * 自动化测试任务ID
     */
    private Long autotestTaskId;

    /**
     * 自动化测试任务创建者ID（不是uid）
     */
    private Long autotestCreator;

    /**
     * 1==经改造后，用mysql存储
     */
    private Integer useMysql;

    public HdzjComponentDefine() {}
}