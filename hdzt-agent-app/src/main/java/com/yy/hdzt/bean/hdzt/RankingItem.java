package com.yy.hdzt.bean.hdzt;

import lombok.Data;
import java.util.Date;

/**
 * 计榜项目表实体类
 */
@Data
public class RankingItem {
    /**
     * 活动标识
     */
    private Integer actId;

    /**
     * 榜单标识
     */
    private Integer rankId;

    /**
     * 项目列表，用逗号分开，格式如：BBT,MG,23445
     */
    private String itemIds;

    /**
     * 项目别称, 项目列表中的所有id的别称，可用于项目ID转换场合
     */
    private String itemAlias;

    /**
     * 相关说明
     */
    private String remark;

    /**
     * 扩展数据
     */
    private String extjson;

    /**
     * 创建时间
     */
    private Date ctime;

    /**
     * 修改时间
     */
    private Date utime;
}
