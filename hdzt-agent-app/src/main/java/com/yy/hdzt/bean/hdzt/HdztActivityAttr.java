package com.yy.hdzt.bean.hdzt;

import lombok.Data;
import java.util.Date;

/**
 * 活动信息属性表实体类
 */
@Data
public class HdztActivityAttr {
    /**
     * 活动标识
     */
    private Long actId;

    /**
     * 属性名称
     */
    private String attrname;

    /**
     * 属性取值
     */
    private String attrvalue;

    /**
     * 属性说明
     */
    private String remark;

    /**
     * 创建时间
     */
    private Date ctime;

    /**
     * 修改时间
     */
    private Date utime;
}
