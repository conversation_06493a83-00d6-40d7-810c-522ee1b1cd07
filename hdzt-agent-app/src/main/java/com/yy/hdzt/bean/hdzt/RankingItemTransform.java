package com.yy.hdzt.bean.hdzt;

import lombok.Data;
import java.util.Date;

/**
 * 项目标识转换表实体类
 */
@Data
public class RankingItemTransform {
    /**
     * 活动标识
     */
    private Integer actId;

    /**
     * 业务标识
     */
    private Integer busiId;

    /**
     * 业务项目标识
     */
    private String busiItemId;

    /**
     * 中台项目标识
     */
    private String hdztItemId;

    /**
     * 项目名称
     */
    private String itemName;

    /**
     * 项目图片URL
     */
    private String itemUrl;

    /**
     * 礼物累的积分，荣耀值（RMB*100）
     */
    private Long itemScore;

    /**
     * 转换说明
     */
    private String remark;

    /**
     * 扩展数据
     */
    private String extjson;

    /**
     * 创建时间
     */
    private Date ctime;

    /**
     * 修改时间
     */
    private Date utime;
}
