package com.yy.hdzt.bean.hdzt;

import lombok.Data;
import java.util.Date;

/**
 * 活动信息表实体类
 */
@Data
public class HdztActivity {
    /**
     * 活动标识, 故意设计成非自增，方便和业务自身对接
     */
    private Integer actId;

    /**
     * 业务标识
     */
    private Integer busiId;

    /**
     * 活动名称
     */
    private String actName;

    /**
     * 活动状态, 1-有效， 其它值-无效 77-归档状态，设置成77，所以redis操作都会指向act_group_archive，切记要完成数据归档再设置成77
     */
    private Byte status;

    /**
     * 活动开始时间
     */
    private Date beginTime;

    /**
     * 活动结束时间
     */
    private Date endTime;

    /**
     * 展示开始时间
     */
    private Date beginTimeShow;

    /**
     * 展示结束时间
     */
    private Date endTimeShow;

    /**
     * 活动类型
     */
    private Integer actType;

    /**
     * 活动群组 - 性能优化用，相同群组榜放同一redis实例，确保lua、pipeline 等完整执行，目前只能为2/3/4（1系统专用，具体活动请勿使用！）
     */
    private Integer actGroup;

    /**
     * 活动群组，redis数据归档实例
     */
    private Integer actGroupArchive;

    /**
     * 安全钥匙，用于多业务部门间活动区隔保密，只能由数字和字母组成，出现其它字符不保证正常
     */
    private String secret;

    /**
     * 活动背景图片
     */
    private String actBgUrl;

    /**
     * 活动详情跳转url
     */
    private String detailUrl;

    /**
     * 活动说明
     */
    private String remark;

    /**
     * 扩展数据
     */
    private String extjson;

    /**
     * 创建时间
     */
    private Date ctime;

    /**
     * 修改时间
     */
    private Date utime;
}
