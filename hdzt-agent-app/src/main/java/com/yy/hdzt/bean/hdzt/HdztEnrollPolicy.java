package com.yy.hdzt.bean.hdzt;

import lombok.Data;

/**
 * 参赛报名策略表实体类
 */
@Data
public class HdztEnrollPolicy {
    /**
     * 活动标识
     */
    private Integer actId;

    /**
     * 角色标识，用于控制该角色如何做报名检查
     */
    private Integer roleId;

    /**
     * 报名检查标志, 0-不检查，1-要检查 | 不存在或检查标记为0，则不做报名处理，用回上报本身的角色
     */
    private Integer checkFlag;

    /**
     * 转换标志，当有报名记录时：0-不转成dest role， 1-转成dest role
     */
    private Integer convertFlag;

    /**
     * 通过方式，当没有报名时：0-不通过，1-通过 |  0适用于提前初始化报名数据才有累榜资格的场景
     */
    private Integer passWay;

    /**
     * 自动报名，当没有报名时：0-不自动报名，1-自动报名
     */
    private Integer autoEnroll;

    /**
     * 扩展数据
     */
    private String extjson;

    /**
     * 备注信息
     */
    private String remark;
}
