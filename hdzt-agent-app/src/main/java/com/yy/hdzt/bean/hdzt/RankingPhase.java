package com.yy.hdzt.bean.hdzt;

import lombok.Data;
import java.util.Date;

/**
 * 排行榜阶段表实体类
 */
@Data
public class RankingPhase {
    /**
     * 活动标识
     */
    private Long actId;

    /**
     * 奖品标识
     */
    private Integer phaseId;

    /**
     * 奖品类型：1:实物奖品 2:YY平台虚拟资源 3:道具 4:点卡点券之类
     */
    private String phaseName;

    /**
     * 给前端展示的阶段名称模板
     */
    private String phaseNameShow;

    /**
     * 阶段分组
     */
    private String phaseGroupCode;

    /**
     * 1-有效， 其它值-无效
     */
    private Byte status;

    /**
     * 中奖概率
     */
    private String phaseBgUrl;

    /**
     * 任务中心道具标识
     */
    private Date beginTime;

    /**
     * 道具数量
     */
    private Date endTime;

    /**
     * 展示开始时间
     */
    private Date showBeginTime;

    /**
     * 展示结束时间
     */
    private Date showEndTime;

    /**
     * 2021年12月15日已废弃
     */
    private Integer taskValueType;

    /**
     * 阶段任务可循环完成次数，0：不重置，大于0：重置的次数， 小于0：不限次数无限循环
     */
    private Integer recycleCount;

    /**
     * 任务循环类型， 1:全任务循环，2：最后一级任务循环，其它值无效会导致任务不更新（2022年08月01重新定义使用）
     */
    private Integer recycleType;

    /**
     * 备注
     */
    private String remark;

    /**
     * 扩展数据
     */
    private String extjson;

    /**
     * 创建时间
     */
    private Date ctime;

    /**
     * 修改时间
     */
    private Date utime;
}
