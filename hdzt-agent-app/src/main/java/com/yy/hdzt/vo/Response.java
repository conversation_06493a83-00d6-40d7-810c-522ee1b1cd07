package com.yy.hdzt.vo;

import lombok.Data;

/**
 * <AUTHOR> 2025/7/31
 */
@Data
public class Response<T> {

    private int code;

    private String message;

    private T data;

    public Response(T data) {
        this.data = data;
    }

    public Response(int code, String message) {
        this.code = code;
        this.message = message;
    }

    public static <T> Response<T> success(T t) {
        return new Response<>(t);
    }

    public  static <T> Response<T> fail(String message) {
        return new Response<>(1, message);
    }

}
