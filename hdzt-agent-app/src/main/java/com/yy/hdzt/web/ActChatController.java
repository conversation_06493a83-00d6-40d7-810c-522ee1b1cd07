package com.yy.hdzt.web;

import com.yy.hdzt.handler.ActConfigHandler;
import com.yy.hdzt.service.HdzjComponentAttrService;
import com.yy.hdzt.service.HdztActivityCleanupService;
import com.yy.hdzt.service.HdztInitService;
import com.yy.hdzt.template.ActConfigTemplate;
import com.yy.hdzt.util.ActConfigJsonParser;
import com.yy.hdzt.vo.ActChatReq;
import com.yy.java.component.util.JsonUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * <AUTHOR> 2025/7/17
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/act")
public class ActChatController {

    private final ActConfigHandler actConfigHandler;

    private final ActConfigJsonParser actConfigJsonParser;

    private final HdztInitService hdztInitService;

    private final HdztActivityCleanupService hdztActivityCleanupService;

    private final HdzjComponentAttrService hdzjComponentAttrService;

    @PostMapping("/chat")
    public String chat(@RequestBody ActChatReq req) {
        Assert.notNull(req.getSessionId(), "sessionId为空");
        Assert.notNull(req.getDocument(), "document为空");
        return actConfigHandler.chat(req, Map.of());
    }

    /**
     * 根据json init Act
     * @return
     */
    @GetMapping("/initAct")
    public String initAct() {
        try {
            // 使用新的解析器解析复杂的 JSON 响应
            ActConfigTemplate actConfigTemplate = JsonUtils.deserialize(JSON, ActConfigTemplate.class);

            // 验证解析结果
            if (!actConfigJsonParser.validateActConfigTemplate(actConfigTemplate)) {
                return "解析的 ActConfigTemplate 验证失败";
            }
            hdztInitService.initAct(actConfigTemplate);
        } catch (Exception e) {
            return "处理失败: " + e.getMessage();
        }
        return "success";
    }

    /**
     * 初始化活动组件
     * 处理README格式的JSON数据并插入到hdzj_component_attr表
     *
     * @param jsonContent 从请求头获取的JSON内容，如果为空则使用默认的HDZKJSON
     * @return 处理结果
     */
    @PostMapping("/initActComponent")
    public String initActComponent(@RequestBody String jsonContent) {
        try {
            log.info("开始初始化活动组件");

            // 如果请求头中没有jsonContent，使用默认的HDZKJSON
            String actualJsonContent = (jsonContent != null && !jsonContent.trim().isEmpty())
                ? jsonContent : HDZKJSON;

            log.info("使用的JSON内容长度: {}", actualJsonContent.length());

            // 调用组件属性服务处理JSON数据
            int insertCount = hdzjComponentAttrService.initActComponent(actualJsonContent);

            return String.format("活动组件初始化成功！共插入 %d 条组件属性记录", insertCount);

        } catch (Exception e) {
            log.error("初始化活动组件失败: {}", e.getMessage(), e);
            return "初始化活动组件失败: " + e.getMessage();
        }
    }

    /**
     * 清理活动榜单配置
     * @param dbName
     * @param actId
     * @return
     */
    @GetMapping("/clearAct")
    public String clearAct(@RequestParam String dbName, @RequestParam Long actId) {
        try {
            // 参数验证
            Assert.hasText(dbName, "数据库名称不能为空");
            Assert.notNull(actId, "活动ID不能为空");
            Assert.isTrue(actId > 0, "活动ID必须是正整数");

            // 执行清理操作
            HdztActivityCleanupService.ActivityCleanupResult result =
                hdztActivityCleanupService.deleteActivityData(dbName, actId);

            if (result.isSuccess()) {
                return String.format("清理成功 - 数据库: %s, 活动ID: %d, 总计删除: %d 条记录\n详细信息:\n" +
                                "- 活动信息: %d 条\n" +
                                "- 活动属性: %d 条\n" +
                                "- 报名策略: %d 条\n" +
                                "- 榜单配置: %d 条\n" +
                                "- 排行榜阶段: %d 条\n" +
                                "- 阶段分组: %d 条\n" +
                                "- 计榜成员: %d 条\n" +
                                "- 计榜项目: %d 条\n" +
                                "- 项目转换: %d 条\n" +
                                "- 组件属性: %d 条",
                        dbName, actId, result.getTotalCount(),
                        result.getActivityCount(), result.getActivityAttrCount(),
                        result.getEnrollPolicyCount(), result.getRankingConfigCount(),
                        result.getRankingPhaseCount(), result.getRankingPhaseGroupCount(),
                        result.getRankingMemberCount(), result.getRankingItemCount(),
                        result.getRankingItemTransformCount(), result.getComponentAttrCount());
            } else {
                return "清理失败: " + result.getMessage();
            }
        } catch (Exception e) {
            return "处理失败: " + e.getMessage();
        }
    }

    public final static String HDZKJSON = """
            {
              "2025071009|1016|810": {
                "moduleName":"聊天室七夕勋章",
                "actId": 2025073001,
                "cmptId": 1016,
                "cmptUseInx": 810,
                "busiId": 400,
                "giftIds": ["20001", "20002"],
                "apps": ["yy"],
                "giftIcons": {
                  "20001": "gift_icon_20001",
                  "20002": "gift_icon_20002"
                },
                "tAwardPkgInfos": {
                  "2001": {
                    "medalPic": "medal_primary_pic_2",
                    "medalName": "新手勋章",
                    "qrCode": "qr_code_primary_2"
                  },
                  "2002": {
                    "medalPic": "medal_senior_pic_2",
                    "medalName": "进阶勋章",
                    "qrCode": "qr_code_senior_2"
                  }
                },
                "tAwardTskMedalId": 2002,
                "tAwardPkgPrimaryId": 2001,
                "tAwardPkgSeniorId": 2002,
                "tAwardPkgExpertId": 0,
                "expertRankId": 0,
                "expertPhaseId": 0,
                "threshold": 50000
              }
            }
            """;


    public final static String JSON = "{\n" +
            "  \"busiId\": \"810\",\n" +
            "  \"actId\": \"2025081001\",\n" +
            "  \"actGroup\": \"1\",\n" +
            "  \"hdztDb\": \"hdzt\",\n" +
            "  \"hdzkDb\": \"hdzk\",\n" +
            "  \"actName\": \"七夕浪漫爱情盛典活动\",\n" +
            "  \"beginTime\": \"2025-08-18 17:00:00\",\n" +
            "  \"endTime\": \"2025-08-31 23:59:59\",\n" +
            "  \"actRoles\": [\n" +
            "    {\n" +
            "      \"roleId\": \"51003\",\n" +
            "      \"remark\": \"送礼用户\",\n" +
            "      \"autoEnroll\": \"1\",\n" +
            "      \"checkFlag\": \"0\",\n" +
            "      \"passWay\": \"1\",\n" +
            "      \"convertFlag\": \"0\"\n" +
            "    },\n" +
            "    {\n" +
            "      \"roleId\": \"81001\",\n" +
            "      \"remark\": \"聊天室签约主持\",\n" +
            "      \"autoEnroll\": \"1\",\n" +
            "      \"checkFlag\": \"0\",\n" +
            "      \"passWay\": \"1\",\n" +
            "      \"convertFlag\": \"0\"\n" +
            "    },\n" +
            "    {\n" +
            "      \"roleId\": \"81050\",\n" +
            "      \"remark\": \"经营厅\",\n" +
            "      \"autoEnroll\": \"1\",\n" +
            "      \"checkFlag\": \"0\",\n" +
            "      \"passWay\": \"1\",\n" +
            "      \"convertFlag\": \"0\"\n" +
            "    },\n" +
            "    {\n" +
            "      \"roleId\": \"81070\",\n" +
            "      \"remark\": \"语音房家族\",\n" +
            "      \"autoEnroll\": \"1\",\n" +
            "      \"checkFlag\": \"0\",\n" +
            "      \"passWay\": \"1\",\n" +
            "      \"convertFlag\": \"0\"\n" +
            "    }\n" +
            "  ],\n" +
            "  \"actGifts\": [\n" +
            "    {\n" +
            "      \"busiItemId\": \"普通礼物0.1Y\",\n" +
            "      \"hdztItemId\": \"gift_0_1Y\",\n" +
            "      \"itemName\": \"普通礼物\",\n" +
            "      \"itemScore\": \"10\",\n" +
            "      \"remark\": \"礼物栏普通礼物\"\n" +
            "    },\n" +
            "    {\n" +
            "      \"busiItemId\": \"告白礼物1314Y\",\n" +
            "      \"hdztItemId\": \"gift_1314Y\",\n" +
            "      \"itemName\": \"告白礼物\",\n" +
            "      \"itemScore\": \"131400\",\n" +
            "      \"remark\": \"礼物栏普通礼物\"\n" +
            "    },\n" +
            "    {\n" +
            "      \"busiItemId\": \"告白墙礼物2099Y\",\n" +
            "      \"hdztItemId\": \"gift_2099Y\",\n" +
            "      \"itemName\": \"告白墙礼物\",\n" +
            "      \"itemScore\": \"209900\",\n" +
            "      \"remark\": \"告白墙礼物\"\n" +
            "    },\n" +
            "    {\n" +
            "      \"busiItemId\": \"免费礼物\",\n" +
            "      \"hdztItemId\": \"gift_free\",\n" +
            "      \"itemName\": \"免费礼物\",\n" +
            "      \"itemScore\": \"5\",\n" +
            "      \"remark\": \"免费礼物，只算进榜单，不算进玩法（活动结束后1天失效）\"\n" +
            "    },\n" +
            "    {\n" +
            "      \"busiItemId\": \"340095\",\n" +
            "      \"hdztItemId\": \"gift_fan_ticket\",\n" +
            "      \"itemName\": \"粉丝票\",\n" +
            "      \"itemScore\": \"5\",\n" +
            "      \"remark\": \"免费礼物，只算进榜单，不算进玩法（活动结束后1天失效）\"\n" +
            "    },\n" +
            "    {\n" +
            "      \"busiItemId\": \"340342\",\n" +
            "      \"hdztItemId\": \"gift_kun\",\n" +
            "      \"itemName\": \"守护之鲲\",\n" +
            "      \"itemScore\": \"888800\",\n" +
            "      \"remark\": \"海底秘境产出礼物\"\n" +
            "    }\n" +
            "  ],\n" +
            "  \"actSchedules\": [\n" +
            "    {\n" +
            "      \"id\": \"1\",\n" +
            "      \"scheduleName\": \"主持赛程\",\n" +
            "      \"scheduleCode\": \"host_schedule\",\n" +
            "      \"actRankPhases\": [\n" +
            "        {\n" +
            "          \"phaseId\": \"10\",\n" +
            "          \"phaseName\": \"定级赛\",\n" +
            "          \"beginTime\": \"2025-08-18 17:00:00\",\n" +
            "          \"endTime\": \"2025-08-19 23:59:59\"\n" +
            "        },\n" +
            "        {\n" +
            "          \"phaseId\": \"11\",\n" +
            "          \"phaseName\": \"晋级赛\",\n" +
            "          \"beginTime\": \"2025-08-20 00:00:00\",\n" +
            "          \"endTime\": \"2025-08-21 23:59:59\"\n" +
            "        },\n" +
            "        {\n" +
            "          \"phaseId\": \"12\",\n" +
            "          \"phaseName\": \"PK赛\",\n" +
            "          \"beginTime\": \"2025-08-22 00:00:00\",\n" +
            "          \"endTime\": \"2025-08-23 23:59:59\"\n" +
            "        },\n" +
            "        {\n" +
            "          \"phaseId\": \"13\",\n" +
            "          \"phaseName\": \"决赛\",\n" +
            "          \"beginTime\": \"2025-08-24 00:00:00\",\n" +
            "          \"endTime\": \"2025-08-24 23:59:59\"\n" +
            "        }\n" +
            "      ],\n" +
            "      \"actRankConfigs\": [\n" +
            "        {\n" +
            "          \"rankId\": \"100\",\n" +
            "          \"rankName\": \"主持定级赛榜单\",\n" +
            "          \"beginTime\": \"2025-08-18 17:00:00\",\n" +
            "          \"endTime\": \"2025-08-19 23:59:59\",\n" +
            "          \"timeKey\": \"0\",\n" +
            "          \"itemKey\": \"0\",\n" +
            "          \"memberKey\": \"0\",\n" +
            "          \"roles\": \"81001\",\n" +
            "          \"itemIds\": \"gift_0_1Y,gift_1314Y,gift_2099Y,gift_free,gift_fan_ticket,gift_kun\"\n" +
            "        },\n" +
            "        {\n" +
            "          \"rankId\": \"101\",\n" +
            "          \"rankName\": \"主持晋级赛榜单\",\n" +
            "          \"beginTime\": \"2025-08-20 00:00:00\",\n" +
            "          \"endTime\": \"2025-08-21 23:59:59\",\n" +
            "          \"timeKey\": \"0\",\n" +
            "          \"itemKey\": \"0\",\n" +
            "          \"memberKey\": \"0\",\n" +
            "          \"roles\": \"81001\",\n" +
            "          \"itemIds\": \"gift_0_1Y,gift_1314Y,gift_2099Y,gift_free,gift_fan_ticket,gift_kun\"\n" +
            "        },\n" +
            "        {\n" +
            "          \"rankId\": \"102\",\n" +
            "          \"rankName\": \"主持PK赛榜单\",\n" +
            "          \"beginTime\": \"2025-08-22 00:00:00\",\n" +
            "          \"endTime\": \"2025-08-23 23:59:59\",\n" +
            "          \"timeKey\": \"0\",\n" +
            "          \"itemKey\": \"0\",\n" +
            "          \"memberKey\": \"0\",\n" +
            "          \"roles\": \"81001\",\n" +
            "          \"itemIds\": \"gift_0_1Y,gift_1314Y,gift_2099Y,gift_free,gift_fan_ticket,gift_kun\"\n" +
            "        },\n" +
            "        {\n" +
            "          \"rankId\": \"103\",\n" +
            "          \"rankName\": \"主持决赛榜单\",\n" +
            "          \"beginTime\": \"2025-08-24 00:00:00\",\n" +
            "          \"endTime\": \"2025-08-24 23:59:59\",\n" +
            "          \"timeKey\": \"0\",\n" +
            "          \"itemKey\": \"0\",\n" +
            "          \"memberKey\": \"0\",\n" +
            "          \"roles\": \"81001\",\n" +
            "          \"itemIds\": \"gift_0_1Y,gift_1314Y,gift_2099Y,gift_free,gift_fan_ticket,gift_kun\"\n" +
            "        }\n" +
            "      ]\n" +
            "    },\n" +
            "    {\n" +
            "      \"id\": \"2\",\n" +
            "      \"scheduleName\": \"强厅赛程\",\n" +
            "      \"scheduleCode\": \"room_schedule\",\n" +
            "      \"actRankPhases\": [\n" +
            "        {\n" +
            "          \"phaseId\": \"20\",\n" +
            "          \"phaseName\": \"定级赛\",\n" +
            "          \"beginTime\": \"2025-08-25 00:00:00\",\n" +
            "          \"endTime\": \"2025-08-26 23:59:59\"\n" +
            "        },\n" +
            "        {\n" +
            "          \"phaseId\": \"21\",\n" +
            "          \"phaseName\": \"晋级赛\",\n" +
            "          \"beginTime\": \"2025-08-27 00:00:00\",\n" +
            "          \"endTime\": \"2025-08-28 23:59:59\"\n" +
            "        },\n" +
            "        {\n" +
            "          \"phaseId\": \"22\",\n" +
            "          \"phaseName\": \"PK赛\",\n" +
            "          \"beginTime\": \"2025-08-29 00:00:00\",\n" +
            "          \"endTime\": \"2025-08-30 23:59:59\"\n" +
            "        },\n" +
            "        {\n" +
            "          \"phaseId\": \"23\",\n" +
            "          \"phaseName\": \"决赛\",\n" +
            "          \"beginTime\": \"2025-08-31 00:00:00\",\n" +
            "          \"endTime\": \"2025-08-31 23:59:59\"\n" +
            "        }\n" +
            "      ],\n" +
            "      \"actRankConfigs\": [\n" +
            "        {\n" +
            "          \"rankId\": \"200\",\n" +
            "          \"rankName\": \"强厅定级赛榜单\",\n" +
            "          \"beginTime\": \"2025-08-25 00:00:00\",\n" +
            "          \"endTime\": \"2025-08-26 23:59:59\",\n" +
            "          \"timeKey\": \"0\",\n" +
            "          \"itemKey\": \"0\",\n" +
            "          \"memberKey\": \"0\",\n" +
            "          \"roles\": \"81050\",\n" +
            "          \"itemIds\": \"gift_0_1Y,gift_1314Y,gift_2099Y,gift_free,gift_fan_ticket,gift_kun\"\n" +
            "        },\n" +
            "        {\n" +
            "          \"rankId\": \"201\",\n" +
            "          \"rankName\": \"强厅晋级赛榜单\",\n" +
            "          \"beginTime\": \"2025-08-27 00:00:00\",\n" +
            "          \"endTime\": \"2025-08-28 23:59:59\",\n" +
            "          \"timeKey\": \"0\",\n" +
            "          \"itemKey\": \"0\",\n" +
            "          \"memberKey\": \"0\",\n" +
            "          \"roles\": \"81050\",\n" +
            "          \"itemIds\": \"gift_0_1Y,gift_1314Y,gift_2099Y,gift_free,gift_fan_ticket,gift_kun\"\n" +
            "        },\n" +
            "        {\n" +
            "          \"rankId\": \"202\",\n" +
            "          \"rankName\": \"强厅PK赛榜单\",\n" +
            "          \"beginTime\": \"2025-08-29 00:00:00\",\n" +
            "          \"endTime\": \"2025-08-30 23:59:59\",\n" +
            "          \"timeKey\": \"0\",\n" +
            "          \"itemKey\": \"0\",\n" +
            "          \"memberKey\": \"0\",\n" +
            "          \"roles\": \"81050\",\n" +
            "          \"itemIds\": \"gift_0_1Y,gift_1314Y,gift_2099Y,gift_free,gift_fan_ticket,gift_kun\"\n" +
            "        },\n" +
            "        {\n" +
            "          \"rankId\": \"203\",\n" +
            "          \"rankName\": \"强厅决赛榜单\",\n" +
            "          \"beginTime\": \"2025-08-31 00:00:00\",\n" +
            "          \"endTime\": \"2025-08-31 23:59:59\",\n" +
            "          \"timeKey\": \"0\",\n" +
            "          \"itemKey\": \"0\",\n" +
            "          \"memberKey\": \"0\",\n" +
            "          \"roles\": \"81050\",\n" +
            "          \"itemIds\": \"gift_0_1Y,gift_1314Y,gift_2099Y,gift_free,gift_fan_ticket,gift_kun\"\n" +
            "        }\n" +
            "      ]\n" +
            "    },\n" +
            "    {\n" +
            "      \"id\": \"3\",\n" +
            "      \"scheduleName\": \"家族总榜\",\n" +
            "      \"scheduleCode\": \"family_schedule\",\n" +
            "      \"actRankPhases\": [\n" +
            "        {\n" +
            "          \"phaseId\": \"30\",\n" +
            "          \"phaseName\": \"家族总榜\",\n" +
            "          \"beginTime\": \"2025-08-18 17:00:00\",\n" +
            "          \"endTime\": \"2025-08-31 23:59:59\"\n" +
            "        }\n" +
            "      ],\n" +
            "      \"actRankConfigs\": [\n" +
            "        {\n" +
            "          \"rankId\": \"300\",\n" +
            "          \"rankName\": \"家族总榜\",\n" +
            "          \"beginTime\": \"2025-08-18 17:00:00\",\n" +
            "          \"endTime\": \"2025-08-31 23:59:59\",\n" +
            "          \"timeKey\": \"0\",\n" +
            "          \"itemKey\": \"0\",\n" +
            "          \"memberKey\": \"0\",\n" +
            "          \"roles\": \"81070\",\n" +
            "          \"itemIds\": \"gift_0_1Y,gift_1314Y,gift_2099Y,gift_free,gift_fan_ticket,gift_kun\"\n" +
            "        }\n" +
            "      ]\n" +
            "    },\n" +
            "    {\n" +
            "      \"id\": \"4\",\n" +
            "      \"scheduleName\": \"神豪榜单\",\n" +
            "      \"scheduleCode\": \"shenhao_schedule\",\n" +
            "      \"actRankPhases\": [\n" +
            "        {\n" +
            "          \"phaseId\": \"40\",\n" +
            "          \"phaseName\": \"神豪榜单\",\n" +
            "          \"beginTime\": \"2025-08-18 17:00:00\",\n" +
            "          \"endTime\": \"2025-08-31 23:59:59\"\n" +
            "        }\n" +
            "      ],\n" +
            "      \"actRankConfigs\": [\n" +
            "        {\n" +
            "          \"rankId\": \"400\",\n" +
            "          \"rankName\": \"贡献神豪榜单\",\n" +
            "          \"beginTime\": \"2025-08-18 17:00:00\",\n" +
            "          \"endTime\": \"2025-08-31 23:59:59\",\n" +
            "          \"timeKey\": \"0\",\n" +
            "          \"itemKey\": \"0\",\n" +
            "          \"memberKey\": \"0\",\n" +
            "          \"roles\": \"51003\",\n" +
            "          \"itemIds\": \"gift_0_1Y,gift_1314Y,gift_2099Y,gift_free,gift_fan_ticket\"\n" +
            "        },\n" +
            "        {\n" +
            "          \"rankId\": \"401\",\n" +
            "          \"rankName\": \"YO神豪榜单\",\n" +
            "          \"beginTime\": \"2025-08-18 17:00:00\",\n" +
            "          \"endTime\": \"2025-08-31 23:59:59\",\n" +
            "          \"timeKey\": \"0\",\n" +
            "          \"itemKey\": \"0\",\n" +
            "          \"memberKey\": \"0\",\n" +
            "          \"roles\": \"51003\",\n" +
            "          \"itemIds\": \"gift_0_1Y,gift_1314Y,gift_2099Y,gift_free,gift_fan_ticket\"\n" +
            "        },\n" +
            "        {\n" +
            "          \"rankId\": \"402\",\n" +
            "          \"rankName\": \"幸运神豪榜单\",\n" +
            "          \"beginTime\": \"2025-08-18 17:00:00\",\n" +
            "          \"endTime\": \"2025-08-31 23:59:59\",\n" +
            "          \"timeKey\": \"0\",\n" +
            "          \"itemKey\": \"0\",\n" +
            "          \"memberKey\": \"0\",\n" +
            "          \"roles\": \"51003\",\n" +
            "          \"itemIds\": \"gift_0_1Y,gift_1314Y,gift_2099Y,gift_free,gift_fan_ticket,gift_kun\"\n" +
            "        },\n" +
            "        {\n" +
            "          \"rankId\": \"403\",\n" +
            "          \"rankName\": \"百度神豪榜单\",\n" +
            "          \"beginTime\": \"2025-08-18 17:00:00\",\n" +
            "          \"endTime\": \"2025-08-31 23:59:59\",\n" +
            "          \"timeKey\": \"0\",\n" +
            "          \"itemKey\": \"0\",\n" +
            "          \"memberKey\": \"0\",\n" +
            "          \"roles\": \"51003\",\n" +
            "          \"itemIds\": \"gift_0_1Y,gift_1314Y,gift_2099Y,gift_free,gift_fan_ticket\"\n" +
            "        }\n" +
            "      ]\n" +
            "    }\n" +
            "  ]\n" +
            "}";
}
