package com.yy.hdzt.web;

import com.yy.hdzt.vo.*;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> 2025/7/31
 */
@RestController
@RequestMapping("/api/act/rankEvent")
public class ActRankEventController {

    @RequestMapping("/batchList")
    public Response<BatchListResp> batchList(Long actId) {

        // 如果没有传入actId，使用默认值1001
        if (actId == null) {
            actId = 1001L;
        }

        BatchListResp resp = new BatchListResp();

        BatchVo batchVo1 = new BatchVo();
        batchVo1.setActId(actId);
        batchVo1.setBatchId("batch1");
        batchVo1.setStartTime(new Date(System.currentTimeMillis() - 86400000)); // 昨天
        batchVo1.setEndTime(new Date());
        batchVo1.setCreateUid(50042962L);

        BatchVo batchVo2 = new BatchVo();
        batchVo2.setActId(actId);
        batchVo2.setBatchId("batch2");
        batchVo2.setStartTime(new Date());
        batchVo2.setEndTime(new Date(System.currentTimeMillis() + 86400000)); // 明天
        batchVo2.setCreateUid(50042963L);

        BatchVo batchVo3 = new BatchVo();
        batchVo3.setActId(actId);
        batchVo3.setBatchId("batch3");
        batchVo3.setStartTime(new Date(System.currentTimeMillis() + 86400000)); // 明天
        batchVo3.setEndTime(new Date(System.currentTimeMillis() + 172800000)); // 后天
        batchVo3.setCreateUid(50042964L);

        resp.setBatchVoList(List.of(batchVo1, batchVo2, batchVo3));

        return Response.success(resp);
    }

    @RequestMapping("/rankList")
    public Response<RankListResp> rankList(Long actId, String batchId) {

        RankListResp resp = new RankListResp();

        // 创建模拟的分组信息列表
        List<GroupInfo> groupInfos = new ArrayList<>();

        // 第一个分组
        GroupInfo group1 = new GroupInfo();
        group1.setGroupName("预选赛");

        List<PhaseInfo> phaseInfos1 = new ArrayList<>();

        // 第一个阶段
        PhaseInfo phase1 = new PhaseInfo();
        phase1.setPhaseId(1001L);
        phase1.setPhaseName("预选赛第一阶段");
        phase1.setBeginTime(new Date(System.currentTimeMillis() - 86400000)); // 昨天
        phase1.setEndTime(new Date());

        List<RankInfo> rankInfos1 = new ArrayList<>();
        RankInfo rank1 = new RankInfo();
        rank1.setRankId(10001L);
        rank1.setRankName("个人积分榜");
        rankInfos1.add(rank1);

        RankInfo rank2 = new RankInfo();
        rank2.setRankId(10002L);
        rank2.setRankName("团队积分榜");
        rankInfos1.add(rank2);

        phase1.setRankInfos(rankInfos1);
        phaseInfos1.add(phase1);

        // 第二个阶段
        PhaseInfo phase2 = new PhaseInfo();
        phase2.setPhaseId(1002L);
        phase2.setPhaseName("预选赛第二阶段");
        phase2.setBeginTime(new Date());
        phase2.setEndTime(new Date(System.currentTimeMillis() + 86400000)); // 明天

        List<RankInfo> rankInfos2 = new ArrayList<>();
        RankInfo rank3 = new RankInfo();
        rank3.setRankId(10003L);
        rank3.setRankName("新人榜");
        rankInfos2.add(rank3);

        phase2.setRankInfos(rankInfos2);
        phaseInfos1.add(phase2);

        group1.setPhaseInfos(phaseInfos1);
        groupInfos.add(group1);

        // 第二个分组
        GroupInfo group2 = new GroupInfo();
        group2.setGroupName("决赛");

        List<PhaseInfo> phaseInfos2 = new ArrayList<>();

        PhaseInfo phase3 = new PhaseInfo();
        phase3.setPhaseId(2001L);
        phase3.setPhaseName("决赛阶段");
        phase3.setBeginTime(new Date(System.currentTimeMillis() + 172800000)); // 后天
        phase3.setEndTime(new Date(System.currentTimeMillis() + 259200000)); // 大后天

        List<RankInfo> rankInfos3 = new ArrayList<>();
        RankInfo rank4 = new RankInfo();
        rank4.setRankId(20001L);
        rank4.setRankName("总决赛榜");
        rankInfos3.add(rank4);

        phase3.setRankInfos(rankInfos3);
        phaseInfos2.add(phase3);

        group2.setPhaseInfos(phaseInfos2);
        groupInfos.add(group2);

        resp.setGroupInfos(groupInfos);

        return Response.success(resp);
    }

    @RequestMapping("/eventList")
    public Response<EventListResp> eventList(Long actId, String batchId, Long phaseId, Long rankId, String eventType, Date beginTime, Date endTime) {

        EventListResp resp = new EventListResp();

        // 创建模拟的事件信息列表
        List<EventInfo> eventInfos = new ArrayList<>();

        // 事件1
        EventInfo event1 = new EventInfo();
        event1.setEventId(100001L);
        event1.setEventType("SCORE_UPDATE");
        event1.setEventTime(new Date(System.currentTimeMillis() - 3600000)); // 1小时前
        event1.setTraceId("trace_001_" + System.currentTimeMillis());
        eventInfos.add(event1);

        // 事件2
        EventInfo event2 = new EventInfo();
        event2.setEventId(100002L);
        event2.setEventType("RANK_CHANGE");
        event2.setEventTime(new Date(System.currentTimeMillis() - 1800000)); // 30分钟前
        event2.setTraceId("trace_002_" + System.currentTimeMillis());
        eventInfos.add(event2);

        // 事件3
        EventInfo event3 = new EventInfo();
        event3.setEventId(100003L);
        event3.setEventType("USER_JOIN");
        event3.setEventTime(new Date(System.currentTimeMillis() - 900000)); // 15分钟前
        event3.setTraceId("trace_003_" + System.currentTimeMillis());
        eventInfos.add(event3);

        // 事件4
        EventInfo event4 = new EventInfo();
        event4.setEventId(100004L);
        event4.setEventType("REWARD_SEND");
        event4.setEventTime(new Date(System.currentTimeMillis() - 300000)); // 5分钟前
        event4.setTraceId("trace_004_" + System.currentTimeMillis());
        eventInfos.add(event4);

        // 事件5
        EventInfo event5 = new EventInfo();
        event5.setEventId(100005L);
        event5.setEventType("PHASE_START");
        event5.setEventTime(new Date());
        event5.setTraceId("trace_005_" + System.currentTimeMillis());
        eventInfos.add(event5);

        resp.setEventInfos(eventInfos);

        return Response.success(resp);
    }

    @RequestMapping("/eventDetail")
    public Response<EventDetailResp> eventDetail(Long actId, String batchId, Long phaseId, Long rankId, String eventId) {

        EventDetailResp resp = new EventDetailResp();

        // 创建模拟的事件详情列表
        List<EventDetail> eventDetails = new ArrayList<>();

        // 事件详情1 - 用户数据
        EventDetail detail1 = new EventDetail();
        detail1.setDataType("USER_DATA");
        detail1.setCmptId(1001L);
        detail1.setCmptName("用户信息组件");
        detail1.setDataJson("{\"userId\":50042962,\"userName\":\"测试用户1\",\"score\":1500,\"rank\":3,\"avatar\":\"http://example.com/avatar1.jpg\"}");
        eventDetails.add(detail1);

        // 事件详情2 - 积分变化
        EventDetail detail2 = new EventDetail();
        detail2.setDataType("SCORE_CHANGE");
        detail2.setCmptId(1002L);
        detail2.setCmptName("积分变化组件");
        detail2.setDataJson("{\"beforeScore\":1200,\"afterScore\":1500,\"changeAmount\":300,\"changeReason\":\"完成任务\",\"timestamp\":\"2025-07-31 10:30:00\"}");
        eventDetails.add(detail2);

        // 事件详情3 - 排名变化
        EventDetail detail3 = new EventDetail();
        detail3.setDataType("RANK_CHANGE");
        detail3.setCmptId(1003L);
        detail3.setCmptName("排名变化组件");
        detail3.setDataJson("{\"beforeRank\":5,\"afterRank\":3,\"rankChange\":2,\"totalParticipants\":100}");
        eventDetails.add(detail3);

        // 事件详情4 - 奖励信息
        EventDetail detail4 = new EventDetail();
        detail4.setDataType("REWARD_INFO");
        detail4.setCmptId(1004L);
        detail4.setCmptName("奖励信息组件");
        detail4.setDataJson("{\"rewardType\":\"VIRTUAL_GIFT\",\"rewardName\":\"钻石\",\"rewardAmount\":100,\"rewardIcon\":\"http://example.com/diamond.png\"}");
        eventDetails.add(detail4);

        // 事件详情5 - 系统消息
        EventDetail detail5 = new EventDetail();
        detail5.setDataType("SYSTEM_MESSAGE");
        detail5.setCmptId(1005L);
        detail5.setCmptName("系统消息组件");
        detail5.setDataJson("{\"messageType\":\"ACHIEVEMENT\",\"title\":\"恭喜获得成就\",\"content\":\"您已成功进入前三名！\",\"showTime\":3000}");
        eventDetails.add(detail5);

        resp.setEventDetails(eventDetails);

        return Response.success(resp);
    }


}
