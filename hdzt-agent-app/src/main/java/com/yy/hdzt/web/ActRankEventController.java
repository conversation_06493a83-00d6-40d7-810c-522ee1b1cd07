package com.yy.hdzt.web;

import com.yy.hdzt.vo.*;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> 2025/7/31
 */
@RestController
@RequestMapping("/api/act/rankEvent")
public class ActRankEventController {

    @RequestMapping("/batchList")
    public Response<BatchListResp> batchList(Long actId) {

        BatchListResp resp = new BatchListResp();

        BatchVo batchVo1 = new BatchVo();
        batchVo1.setActId(actId);
        batchVo1.setBatchId("batch1");
        batchVo1.setStartTime(new Date());
        batchVo1.setEndTime(new Date());
        batchVo1.setCreateUid(50042962L);

        BatchVo batchVo2 = new BatchVo();
        batchVo1.setActId(actId);
        batchVo1.setBatchId("batch2");
        batchVo1.setStartTime(new Date());
        batchVo1.setEndTime(new Date());
        batchVo1.setCreateUid(50042962L);

        BatchVo batchVo3 = new BatchVo();
        batchVo1.setActId(actId);
        batchVo1.setBatchId("batch3");
        batchVo1.setStartTime(new Date());
        batchVo1.setEndTime(new Date());
        batchVo1.setCreateUid(50042962L);

        resp.setBatchVoList(List.of(batchVo1, batchVo2, batchVo3));

        return Response.success(resp);
    }

    @RequestMapping("/rankList")
    public Response<RankListResp> rankList(Long actId, String batchId) {

        RankListResp resp = new RankListResp();

        return Response.success(new RankListResp());
    }

    @RequestMapping("/eventList")
    public Response<EventListResp> eventList(Long actId, String batchId, Long phaseId, Long rankId, String eventType, Date beginTime, Date endTime) {

        EventListResp resp = new EventListResp();

        return Response.success(resp);
    }




}
