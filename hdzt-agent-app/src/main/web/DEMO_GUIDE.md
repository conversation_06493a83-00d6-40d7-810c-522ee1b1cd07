# 演示指南

## 前端界面演示步骤

### 1. 启动服务

#### 启动后端服务
```bash
# 在项目根目录下
cd hdzt-agent-app
mvn spring-boot:run
```
后端服务将在 `http://localhost:8080` 启动

#### 启动前端服务
```bash
# 在前端目录下
cd hdzt-agent-app/src/main/web
npm run dev
```
前端服务将在 `http://localhost:5173` 启动

### 2. 访问应用

打开浏览器访问：`http://localhost:5173`

### 3. 演示流程

#### 步骤1：首页 - 查询批次列表
1. 在输入框中输入活动ID，例如：`1001`
2. 点击"查询批次列表"按钮
3. 系统将显示该活动下的所有批次
4. 每个批次显示：批次ID、创建者、开始时间、结束时间

#### 步骤2：赛程榜单页面
1. 点击任意一个批次卡片
2. 进入赛程榜单页面，显示该批次下的赛程分组
3. 每个分组以卡片形式展示，包含：
   - 分组名称（如"预选赛"、"决赛"）
   - 时间轴显示各个阶段
   - 每个阶段显示开始/结束时间和阶段名称
   - 阶段上方显示可点击的榜单列表

#### 步骤3：事件列表页面
1. 点击任意一个榜单名称（如"个人积分榜"）
2. 进入事件列表页面，显示该榜单的所有事件
3. 页面功能：
   - 顶部筛选条件：事件类型、时间范围
   - 表格显示事件列表：事件ID、类型、时间、追踪ID
   - 支持分页和排序
   - 每行有"查看详情"按钮

#### 步骤4：事件详情页面
1. 点击任意事件的"查看详情"按钮
2. 进入事件详情页面，显示该事件的完整业务数据
3. 页面内容：
   - 按数据类型分组显示（用户数据、积分变化、排名变化等）
   - 每个数据类型以卡片形式展示
   - JSON格式化显示业务数据
   - 支持数据复制和查看

### 4. 模拟数据说明

后端接口返回的模拟数据包括：

#### 批次数据
- 3个批次：batch1, batch2, batch3
- 不同的创建者ID和时间戳

#### 赛程榜单数据
- 2个分组：预选赛、决赛
- 预选赛包含2个阶段，决赛包含1个阶段
- 每个阶段包含不同的榜单（个人积分榜、团队积分榜、新人榜等）

#### 事件数据
- 5种事件类型：SCORE_UPDATE、RANK_CHANGE、USER_JOIN、REWARD_SEND、PHASE_START
- 每个事件包含时间戳和追踪ID

#### 事件详情数据
- 5种数据类型：用户数据、积分变化、排名变化、奖励信息、系统消息
- 每种类型包含JSON格式的业务数据

### 5. 界面特性

#### 响应式设计
- 支持不同屏幕尺寸
- 卡片布局自适应

#### 交互体验
- 悬停效果
- 加载状态
- 错误提示
- 面包屑导航

#### 数据展示
- 时间轴组件展示阶段
- 表格组件展示事件列表
- 描述列表展示详情
- JSON格式化显示

### 6. 技术亮点

- **TypeScript**：完整的类型定义
- **Ant Design**：专业的UI组件
- **React Router**：单页应用路由
- **Axios**：HTTP请求封装
- **Day.js**：日期格式化
- **代理配置**：开发环境API代理

### 7. 扩展建议

1. **添加搜索功能**：在事件列表中添加关键词搜索
2. **导出功能**：支持导出事件数据为Excel
3. **实时更新**：使用WebSocket实现事件实时推送
4. **权限控制**：添加用户登录和权限管理
5. **数据可视化**：添加图表展示事件统计信息
