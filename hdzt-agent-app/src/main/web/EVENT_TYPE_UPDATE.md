# 事件类型显示更新说明

## 🎯 **主要改进**

### ✅ **1. 批次列表显示活动ID**
- **新增活动ID显示**：在批次卡片中显示对应的活动ID
- **信息层次优化**：活动ID使用14px字体，突出重要性
- **布局调整**：批次ID、活动ID、创建者按重要性排列

### ✅ **2. 事件详情页面显示事件类型**
- **事件类型标签**：使用彩色Tag显示事件类型
- **事件时间显示**：显示具体的事件发生时间
- **榜单信息显示**：显示事件所属的榜单名称
- **信息传递链路**：从事件列表到事件详情的完整信息传递

## 🔧 **技术实现详情**

### **批次列表信息显示**
```typescript
// HomePage.tsx - 批次卡片信息层次
<div>
  <h3>批次: {batch.batchId}</h3>           // 主标题，蓝色
  <p>活动ID: {batch.actId}</p>             // 14px，灰色
  <p>创建者: {batch.createUid}</p>         // 12px，灰色
</div>
```

### **事件信息传递机制**
```typescript
// EventListPage.tsx - 传递事件信息
const handleEventClick = (eventId, eventType, eventTime) => {
  navigate(`/event-detail/${actId}/${batchId}/${phaseId}/${rankId}/${eventId}`, {
    state: {
      eventType,    // 事件类型
      eventTime,    // 事件时间
      rankName      // 榜单名称
    }
  });
};

// EventDetailPage.tsx - 接收事件信息
const eventType = location.state?.eventType;
const eventTime = location.state?.eventTime;
const rankName = location.state?.rankName;
```

### **事件类型颜色映射**
```typescript
const getEventTypeColor = (type: string) => {
  const colorMap = {
    'SCORE_UPDATE': 'blue',      // 积分更新 - 蓝色
    'RANK_CHANGE': 'green',      // 排名变化 - 绿色
    'USER_JOIN': 'orange',       // 用户加入 - 橙色
    'REWARD_SEND': 'purple',     // 奖励发送 - 紫色
    'PHASE_START': 'red',        // 阶段开始 - 红色
  };
  return colorMap[type] || 'default';
};

const getEventTypeName = (type: string) => {
  const nameMap = {
    'SCORE_UPDATE': '积分更新',
    'RANK_CHANGE': '排名变化',
    'USER_JOIN': '用户加入',
    'REWARD_SEND': '奖励发送',
    'PHASE_START': '阶段开始',
  };
  return nameMap[type] || type;
};
```

## 🎨 **视觉设计改进**

### **批次列表卡片**
```
批次: batch1                    开始时间: 2025-07-30 22:xx:xx
活动ID: 1001                    结束时间: 2025-07-31 22:xx:xx  
创建者: 50042962
```

### **事件详情页面标题**
```
事件详情 - ID: 100001  [积分更新]
事件时间: 2025-07-31 22:xx:xx
所属榜单: 个人积分榜
```

### **事件类型标签样式**
- **积分更新**：蓝色标签 `blue`
- **排名变化**：绿色标签 `green`
- **用户加入**：橙色标签 `orange`
- **奖励发送**：紫色标签 `purple`
- **阶段开始**：红色标签 `red`

## 📱 **用户体验提升**

### **信息完整性**
- **批次页面**：用户可以清楚看到每个批次对应的活动ID
- **事件详情**：用户可以了解事件的完整上下文信息
- **类型识别**：通过颜色标签快速识别事件类型

### **导航连贯性**
1. **批次列表** → 显示活动ID，便于识别
2. **事件列表** → 点击事件传递完整信息
3. **事件详情** → 显示事件类型、时间、榜单等完整信息

### **视觉层次**
- **主要信息**：批次ID、事件ID使用较大字体
- **重要信息**：活动ID、事件类型使用标签突出显示
- **辅助信息**：创建者、时间等使用较小字体

## 🔄 **数据流转**

### **完整的信息传递链路**
```
HomePage (批次列表)
├── 显示: 批次ID、活动ID、创建者、时间
└── 点击批次 →

RankListPage (赛程榜单)
├── 传递: 活动ID、批次ID
└── 点击榜单 →

EventListPage (事件列表)
├── 显示: 事件ID、类型、时间、追踪ID
├── 传递: 阶段时间范围
└── 点击事件 →

EventDetailPage (事件详情)
├── 接收: 事件类型、时间、榜单名称
├── 显示: 完整事件上下文信息
└── 展示: 事件业务数据详情
```

### **状态管理**
- **路由状态传递**：使用React Router的state传递页面间信息
- **信息保持**：确保用户在页面间跳转时不丢失上下文
- **回退支持**：支持浏览器前进后退，保持状态一致

## 🚀 **性能优化**

### **信息传递优化**
- **按需传递**：只传递必要的信息，避免冗余数据
- **状态缓存**：利用React Router的state机制缓存信息
- **懒加载**：事件详情页面按需加载具体数据

### **渲染优化**
- **条件渲染**：只在有数据时显示相应信息
- **样式复用**：事件类型颜色映射复用，减少重复计算
- **组件优化**：使用Ant Design的Tag组件，性能稳定

这些改进让用户能够更清晰地了解数据的完整上下文，提升了整体的用户体验！
