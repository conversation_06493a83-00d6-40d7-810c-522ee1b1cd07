# 活动排行榜事件查询系统

基于React + TypeScript + Vite + Ant Design构建的前端应用，用于查询和管理活动排行榜事件。

## 功能特性

### 1. 首页 - 活动批次查询
- 输入活动ID查询对应的批次列表
- 显示批次的基本信息（批次ID、创建者、时间范围）
- 点击批次卡片进入赛程榜单页面

### 2. 赛程榜单页面
- 以卡片形式展示不同的赛程分组
- 每个赛程使用时间轴展示多个阶段
- 时间轴显示阶段的开始和结束时间
- 每个阶段下方显示可点击的榜单列表

### 3. 事件列表页面
- 显示指定榜单的所有事件
- 支持按事件类型筛选（积分更新、排名变化、用户加入等）
- 支持按时间范围筛选
- 表格形式展示事件详情
- 点击事件可查看详细信息

### 4. 事件详情页面
- 显示事件的完整业务数据
- 按数据类型分组展示（用户数据、积分变化、排名变化等）
- JSON格式化显示业务数据
- 支持数据复制和查看

## 技术栈

- **React 18** - 前端框架
- **TypeScript** - 类型安全
- **Vite** - 构建工具
- **Ant Design** - UI组件库
- **React Router** - 路由管理
- **Axios** - HTTP请求
- **Day.js** - 日期处理

## 快速开始

### 安装依赖
```bash
npm install
```

### 启动开发服务器
```bash
npm run dev
```

### 构建生产版本
```bash
npm run build
```

## API接口

应用通过以下接口与后端通信：

- `GET /api/act/rankEvent/batchList` - 获取批次列表
- `GET /api/act/rankEvent/rankList` - 获取赛程榜单
- `GET /api/act/rankEvent/eventList` - 获取事件列表
- `GET /api/act/rankEvent/eventDetail` - 获取事件详情

## 代理配置

开发环境下，Vite会将`/api`请求代理到`http://localhost:8080`，确保后端服务在该端口运行。

## 页面路由

- `/` - 首页（活动批次查询）
- `/rank-list/:actId/:batchId` - 赛程榜单页面
- `/event-list/:actId/:batchId/:phaseId/:rankId` - 事件列表页面
- `/event-detail/:actId/:batchId/:phaseId/:rankId/:eventId` - 事件详情页面

## 使用说明

1. **启动后端服务**：确保Java后端服务运行在8080端口
2. **启动前端服务**：运行`npm run dev`，前端服务将在3000端口启动
3. **访问应用**：打开浏览器访问`http://localhost:5173`
4. **输入活动ID**：在首页输入活动ID（如：1001）查询批次列表
5. **浏览数据**：依次点击批次、榜单、事件来查看详细信息

## 项目结构

```
src/
├── pages/           # 页面组件
│   ├── HomePage.tsx         # 首页
│   ├── RankListPage.tsx     # 赛程榜单页
│   ├── EventListPage.tsx    # 事件列表页
│   └── EventDetailPage.tsx  # 事件详情页
├── services/        # API服务
│   └── api.ts              # API接口定义
├── App.tsx         # 主应用组件
└── main.tsx        # 应用入口
```
