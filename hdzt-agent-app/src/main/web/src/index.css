:root {
  font-family: Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  color-scheme: light;
  color: #213547;
  background-color: #ffffff;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

a {
  font-weight: 500;
  color: #1890ff;
  text-decoration: inherit;
}
a:hover {
  color: #40a9ff;
}

body {
  margin: 0;
  padding: 0;
  min-width: 100%;
  min-height: 100vh;
  background-color: #f5f5f5;
}

#root {
  width: 100%;
  min-height: 100vh;
}

h1, h2, h3, h4, h5, h6 {
  margin: 0;
  font-weight: 600;
}

* {
  box-sizing: border-box;
}

.ant-layout {
  min-height: 100vh;
}

.ant-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
}

.ant-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  transition: box-shadow 0.3s ease;
}
