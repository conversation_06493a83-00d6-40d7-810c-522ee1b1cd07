.App {
  width: 100%;
  min-height: 100vh;
  background-color: #f5f5f5;
}

/* 页面容器样式 */
.page-container {
  width: 100%;
  max-width: 1400px;
  margin: 0 auto;
  padding: 24px;
  min-height: 100vh;
}

/* 响应式布局 */
@media (max-width: 768px) {
  .page-container {
    padding: 16px;
  }
}

/* 卡片网格布局 */
.card-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 24px;
  width: 100%;
}

@media (max-width: 768px) {
  .card-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
}

/* 时间轴样式优化 */
.ant-timeline-item-content {
  min-height: 60px;
  width: 100%;
}

/* 横向时间轴样式 */
.ant-timeline.ant-timeline-horizontal {
  overflow-x: auto;
  padding: 20px 0;
}

.ant-timeline.ant-timeline-horizontal .ant-timeline-item {
  min-width: 200px;
  padding-bottom: 20px;
}

.ant-timeline.ant-timeline-horizontal .ant-timeline-item-content {
  min-height: auto;
  text-align: center;
  padding-top: 16px;
}

/* 表格样式优化 */
.ant-table-tbody > tr:hover > td {
  background-color: #f5f5f5 !important;
}

/* JSON显示样式 */
pre {
  white-space: pre-wrap;
  word-wrap: break-word;
  max-width: 100%;
  overflow-x: auto;
}

/* 确保所有内容占满宽度 */
.ant-card,
.ant-table-wrapper,
.ant-form {
  width: 100%;
}
