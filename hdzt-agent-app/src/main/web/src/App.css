.App {
  width: 100%;
  min-height: 100vh;
  background-color: #f5f5f5;
}

/* 页面容器样式 */
.page-container {
  width: 100%;
  max-width: 1400px;
  margin: 0 auto;
  padding: 24px;
  min-height: 100vh;
}

/* 响应式布局 */
@media (max-width: 768px) {
  .page-container {
    padding: 16px;
  }
}

/* 卡片网格布局 */
.card-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 24px;
  width: 100%;
}

@media (max-width: 768px) {
  .card-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
}

/* 自定义横向时间轴样式 */
.custom-timeline {
  position: relative;
  overflow-x: auto;
  padding: 20px 0;
}

.custom-timeline::-webkit-scrollbar {
  height: 6px;
}

.custom-timeline::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.custom-timeline::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.custom-timeline::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 时间轴节点悬停效果 */
.timeline-node:hover {
  transform: scale(1.2);
  transition: transform 0.2s ease;
}

/* 表格样式优化 */
.ant-table-tbody > tr:hover > td {
  background-color: #f5f5f5 !important;
}

/* JSON显示样式 */
pre {
  white-space: pre-wrap;
  word-wrap: break-word;
  max-width: 100%;
  overflow-x: auto;
}

/* 确保所有内容占满宽度 */
.ant-card,
.ant-table-wrapper,
.ant-form {
  width: 100%;
}
