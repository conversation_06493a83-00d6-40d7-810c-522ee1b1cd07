.App {
  min-height: 100vh;
  background-color: #f5f5f5;
}

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', '<PERSON><PERSON>s', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

* {
  box-sizing: border-box;
}

.ant-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.ant-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  transition: box-shadow 0.3s ease;
}

.ant-timeline-item-content {
  min-height: 60px;
}

.ant-table-tbody > tr:hover > td {
  background-color: #f5f5f5 !important;
}

pre {
  white-space: pre-wrap;
  word-wrap: break-word;
}
