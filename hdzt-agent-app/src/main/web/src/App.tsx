import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { ConfigProvider } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import HomePage from './pages/HomePage';
import RankListPage from './pages/RankListPage';
import EventListPage from './pages/EventListPage';
import EventDetailPage from './pages/EventDetailPage';
import './App.css';

function App() {
  return (
    <ConfigProvider locale={zhCN}>
      <Router>
        <div className="App">
          <Routes>
            <Route path="/" element={<HomePage />} />
            <Route path="/rank-list/:actId/:batchId" element={<RankListPage />} />
            <Route path="/event-list/:actId/:batchId/:phaseId/:rankId" element={<EventListPage />} />
            <Route path="/event-detail/:actId/:batchId/:phaseId/:rankId/:eventId" element={<EventDetailPage />} />
          </Routes>
        </div>
      </Router>
    </ConfigProvider>
  );
}

export default App;
