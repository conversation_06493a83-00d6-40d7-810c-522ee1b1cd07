import axios from 'axios';

// 创建axios实例
const api = axios.create({
  baseURL: '/api/act/rankEvent',
  timeout: 10000,
});

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    return response.data;
  },
  (error) => {
    console.error('API请求错误:', error);
    return Promise.reject(error);
  }
);

// 类型定义
export interface BatchVo {
  actId: number;
  batchId: string;
  startTime: string;
  endTime: string;
  createUid: number;
}

export interface BatchListResp {
  batchVoList: BatchVo[];
}

export interface RankInfo {
  rankId: number;
  rankName: string;
}

export interface PhaseInfo {
  phaseId: number;
  phaseName: string;
  beginTime: string;
  endTime: string;
  rankInfos: RankInfo[];
}

export interface GroupInfo {
  groupName: string;
  phaseInfos: PhaseInfo[];
}

export interface RankListResp {
  groupInfos: GroupInfo[];
}

export interface EventInfo {
  eventId: number;
  eventType: string;
  eventTime: string;
  traceId: string;
}

export interface EventListResp {
  eventInfos: EventInfo[];
}

export interface EventDetail {
  dataType: string;
  cmptId: number;
  cmptName: string;
  dataJson: string;
}

export interface EventDetailResp {
  eventDetails: EventDetail[];
}

export interface ApiResponse<T> {
  code: number;
  message: string;
  data: T;
}

// API方法
export const apiService = {
  // 获取批次列表
  getBatchList: (actId: number): Promise<ApiResponse<BatchListResp>> => {
    return api.get('/batchList', { params: { actId } });
  },

  // 获取赛程榜单
  getRankList: (actId: number, batchId: string): Promise<ApiResponse<RankListResp>> => {
    return api.get('/rankList', { params: { actId, batchId } });
  },

  // 获取事件列表
  getEventList: (params: {
    actId: number;
    batchId: string;
    phaseId?: number;
    rankId?: number;
    eventType?: string;
    beginTime?: string;
    endTime?: string;
  }): Promise<ApiResponse<EventListResp>> => {
    return api.get('/eventList', { params });
  },

  // 获取事件详情
  getEventDetail: (params: {
    actId: number;
    batchId: string;
    phaseId?: number;
    rankId?: number;
    eventId: string;
  }): Promise<ApiResponse<EventDetailResp>> => {
    return api.get('/eventDetail', { params });
  },
};
