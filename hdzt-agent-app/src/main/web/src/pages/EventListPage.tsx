import React, { useState, useEffect } from 'react';
import { Card, Table, Button, Select, DatePicker, Space, message, Tag } from 'antd';
import { useParams, useNavigate, useLocation } from 'react-router-dom';
import { ArrowLeftOutlined } from '@ant-design/icons';
import { apiService, EventInfo } from '../services/api';
import dayjs from 'dayjs';
import type { ColumnsType } from 'antd/es/table';

const { RangePicker } = DatePicker;

const EventListPage: React.FC = () => {
  const { actId, batchId, phaseId, rankId } = useParams<{
    actId: string;
    batchId: string;
    phaseId: string;
    rankId: string;
  }>();
  const location = useLocation();
  const rankName = location.state?.rankName || '未知榜单';
  const [eventList, setEventList] = useState<EventInfo[]>([]);
  const [loading, setLoading] = useState(false);
  const [eventType, setEventType] = useState<string>('');
  const [dateRange, setDateRange] = useState<[dayjs.Dayjs, dayjs.Dayjs] | null>(null);
  const navigate = useNavigate();

  useEffect(() => {
    fetchEventList();
  }, []);

  const fetchEventList = async () => {
    setLoading(true);
    try {
      const params: any = {
        actId: Number(actId!),
        batchId: batchId!,
        phaseId: Number(phaseId!),
        rankId: Number(rankId!),
      };

      if (eventType) {
        params.eventType = eventType;
      }

      if (dateRange) {
        params.beginTime = dateRange[0].format('YYYY-MM-DD HH:mm:ss');
        params.endTime = dateRange[1].format('YYYY-MM-DD HH:mm:ss');
      }

      const response = await apiService.getEventList(params);
      if (response.code === 0 || !response.code) {
        setEventList(response.data.eventInfos);
      } else {
        message.error(response.message || '获取事件列表失败');
      }
    } catch (error) {
      message.error('网络请求失败');
      console.error('获取事件列表错误:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleEventClick = (eventId: number) => {
    navigate(`/event-detail/${actId}/${batchId}/${phaseId}/${rankId}/${eventId}`);
  };

  const getEventTypeColor = (type: string) => {
    const colorMap: { [key: string]: string } = {
      'SCORE_UPDATE': 'blue',
      'RANK_CHANGE': 'green',
      'USER_JOIN': 'orange',
      'REWARD_SEND': 'purple',
      'PHASE_START': 'red',
    };
    return colorMap[type] || 'default';
  };

  const columns: ColumnsType<EventInfo> = [
    {
      title: '事件ID',
      dataIndex: 'eventId',
      key: 'eventId',
      width: 120,
    },
    {
      title: '事件类型',
      dataIndex: 'eventType',
      key: 'eventType',
      width: 150,
      render: (type: string) => (
        <Tag color={getEventTypeColor(type)}>{type}</Tag>
      ),
    },
    {
      title: '事件时间',
      dataIndex: 'eventTime',
      key: 'eventTime',
      width: 180,
      render: (time: string) => dayjs(time).format('YYYY-MM-DD HH:mm:ss'),
    },
    {
      title: '追踪ID',
      dataIndex: 'traceId',
      key: 'traceId',
      ellipsis: true,
    },
    {
      title: '操作',
      key: 'action',
      width: 100,
      render: (_, record) => (
        <Button
          type="link"
          onClick={() => handleEventClick(record.eventId)}
        >
          查看详情
        </Button>
      ),
    },
  ];

  return (
    <div className="page-container">
      <div style={{ marginBottom: '24px', width: '100%' }}>
        <Button
          icon={<ArrowLeftOutlined />}
          onClick={() => navigate(-1)}
          style={{ marginRight: '16px' }}
        >
          返回上级
        </Button>
        <span style={{ fontSize: '16px', fontWeight: 'bold' }}>
          {rankName} - 事件列表
        </span>
      </div>

      <Card style={{ marginBottom: '24px', width: '100%' }}>
        <Space wrap style={{ width: '100%' }}>
          <Select
            placeholder="选择事件类型"
            style={{ width: 200, minWidth: 150 }}
            allowClear
            value={eventType || undefined}
            onChange={setEventType}
            options={[
              { label: '积分更新', value: 'SCORE_UPDATE' },
              { label: '排名变化', value: 'RANK_CHANGE' },
              { label: '用户加入', value: 'USER_JOIN' },
              { label: '奖励发送', value: 'REWARD_SEND' },
              { label: '阶段开始', value: 'PHASE_START' },
            ]}
          />
          <RangePicker
            showTime
            placeholder={['开始时间', '结束时间']}
            value={dateRange}
            onChange={setDateRange}
            style={{ minWidth: 300 }}
          />
          <Button type="primary" onClick={fetchEventList}>
            查询
          </Button>
        </Space>
      </Card>

      <Card title="事件列表" style={{ width: '100%' }}>
        <Table
          columns={columns}
          dataSource={eventList}
          rowKey="eventId"
          loading={loading}
          scroll={{ x: 800 }}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`,
          }}
        />
      </Card>
    </div>
  );
};

export default EventListPage;
