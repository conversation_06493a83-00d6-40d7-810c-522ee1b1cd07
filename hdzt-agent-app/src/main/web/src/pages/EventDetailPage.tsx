import React, { useState, useEffect } from 'react';
import { Card, Button, message, Spin, Descriptions, Typography, Tag } from 'antd';
import { useParams, useNavigate, useLocation } from 'react-router-dom';
import { ArrowLeftOutlined } from '@ant-design/icons';
import { apiService, EventDetail } from '../services/api';
import dayjs from 'dayjs';

const { Text, Paragraph } = Typography;

const EventDetailPage: React.FC = () => {
  const { actId, batchId, phaseId, rankId, eventId } = useParams<{
    actId: string;
    batchId: string;
    phaseId: string;
    rankId: string;
    eventId: string;
  }>();
  const [eventDetails, setEventDetails] = useState<EventDetail[]>([]);
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();

  // 从路由状态中获取事件信息
  const eventType = location.state?.eventType;
  const eventTime = location.state?.eventTime;
  const rankName = location.state?.rankName;

  useEffect(() => {
    fetchEventDetail();
  }, []);

  const fetchEventDetail = async () => {
    setLoading(true);
    try {
      const response = await apiService.getEventDetail({
        actId: Number(actId!),
        batchId: batchId!,
        phaseId: Number(phaseId!),
        rankId: Number(rankId!),
        eventId: eventId!,
      });
      if (response.code === 0 || !response.code) {
        setEventDetails(response.data.eventDetails);
      } else {
        message.error(response.message || '获取事件详情失败');
      }
    } catch (error) {
      message.error('网络请求失败');
      console.error('获取事件详情错误:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatJsonData = (jsonStr: string) => {
    try {
      const data = JSON.parse(jsonStr);
      return JSON.stringify(data, null, 2);
    } catch {
      return jsonStr;
    }
  };

  const getEventTypeColor = (type: string) => {
    const colorMap: { [key: string]: string } = {
      'SCORE_UPDATE': 'blue',
      'RANK_CHANGE': 'green',
      'USER_JOIN': 'orange',
      'REWARD_SEND': 'purple',
      'PHASE_START': 'red',
    };
    return colorMap[type] || 'default';
  };

  const getEventTypeName = (type: string) => {
    const nameMap: { [key: string]: string } = {
      'SCORE_UPDATE': '积分更新',
      'RANK_CHANGE': '排名变化',
      'USER_JOIN': '用户加入',
      'REWARD_SEND': '奖励发送',
      'PHASE_START': '阶段开始',
    };
    return nameMap[type] || type;
  };

  const getDataTypeColor = (type: string) => {
    const colorMap: { [key: string]: string } = {
      'USER_DATA': '#1890ff',
      'SCORE_CHANGE': '#52c41a',
      'RANK_CHANGE': '#faad14',
      'REWARD_INFO': '#722ed1',
      'SYSTEM_MESSAGE': '#f5222d',
    };
    return colorMap[type] || '#666';
  };

  const getDataTypeName = (type: string) => {
    const nameMap: { [key: string]: string } = {
      'USER_DATA': '用户数据',
      'SCORE_CHANGE': '积分变化',
      'RANK_CHANGE': '排名变化',
      'REWARD_INFO': '奖励信息',
      'SYSTEM_MESSAGE': '系统消息',
    };
    return nameMap[type] || type;
  };

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '100px' }}>
        <Spin size="large" />
      </div>
    );
  }

  return (
    <div className="page-container">
      <div style={{ marginBottom: '24px', width: '100%' }}>
        <Button
          icon={<ArrowLeftOutlined />}
          onClick={() => navigate(-1)}
          style={{ marginRight: '16px' }}
        >
          返回事件列表
        </Button>
        <div style={{ display: 'inline-block' }}>
          <div style={{ fontSize: '16px', fontWeight: 'bold', marginBottom: '4px' }}>
            事件详情 - ID: {eventId}
            {eventType && (
              <Tag
                color={getEventTypeColor(eventType)}
                style={{ marginLeft: '12px' }}
              >
                {getEventTypeName(eventType)}
              </Tag>
            )}
          </div>
          {eventTime && (
            <div style={{ fontSize: '12px', color: '#666' }}>
              事件时间: {dayjs(eventTime).format('YYYY-MM-DD HH:mm:ss')}
            </div>
          )}
          {rankName && (
            <div style={{ fontSize: '12px', color: '#666' }}>
              所属榜单: {rankName}
            </div>
          )}
        </div>
      </div>

      {eventDetails.length === 0 ? (
        <Card style={{ width: '100%' }}>
          <div style={{ textAlign: 'center', padding: '50px', color: '#999' }}>
            暂无事件详情数据
          </div>
        </Card>
      ) : (
        <div style={{ display: 'flex', flexDirection: 'column', gap: '16px', width: '100%' }}>
          {eventDetails.map((detail, index) => (
            <Card
              key={index}
              style={{ width: '100%' }}
              title={
                <div style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '12px',
                  flexWrap: 'wrap'
                }}>
                  <Text
                    strong
                    style={{
                      color: getDataTypeColor(detail.dataType),
                      fontSize: '16px'
                    }}
                  >
                    {getDataTypeName(detail.dataType)}
                  </Text>
                  <Text type="secondary">({detail.cmptName})</Text>
                </div>
              }
              extra={<Text type="secondary">组件ID: {detail.cmptId}</Text>}
            >
              <Descriptions column={1} bordered style={{ width: '100%' }}>
                <Descriptions.Item label="数据类型">
                  <Text code>{detail.dataType}</Text>
                </Descriptions.Item>
                <Descriptions.Item label="组件ID">
                  {detail.cmptId}
                </Descriptions.Item>
                <Descriptions.Item label="组件名称">
                  {detail.cmptName}
                </Descriptions.Item>
                <Descriptions.Item label="业务数据">
                  <div style={{
                    backgroundColor: '#f5f5f5',
                    padding: '12px',
                    borderRadius: '4px',
                    maxHeight: '300px',
                    overflow: 'auto',
                    width: '100%'
                  }}>
                    <Paragraph>
                      <pre style={{
                        margin: 0,
                        fontFamily: 'Monaco, Consolas, monospace',
                        fontSize: '12px',
                        lineHeight: '1.4',
                        maxWidth: '100%',
                        overflow: 'auto'
                      }}>
                        {formatJsonData(detail.dataJson)}
                      </pre>
                    </Paragraph>
                  </div>
                </Descriptions.Item>
              </Descriptions>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
};

export default EventDetailPage;
