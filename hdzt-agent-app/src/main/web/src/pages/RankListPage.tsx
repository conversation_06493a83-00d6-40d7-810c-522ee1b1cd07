import React, { useState, useEffect } from 'react';
import { Card, message, Spin, Button } from 'antd';
import { useParams, useNavigate } from 'react-router-dom';
import { ArrowLeftOutlined } from '@ant-design/icons';
import { apiService, GroupInfo, PhaseInfo, RankInfo } from '../services/api';
import dayjs from 'dayjs';

const RankListPage: React.FC = () => {
  const { actId, batchId } = useParams<{ actId: string; batchId: string }>();
  const [groupList, setGroupList] = useState<GroupInfo[]>([]);
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();

  useEffect(() => {
    if (actId && batchId) {
      fetchRankList();
    }
  }, [actId, batchId]);

  const fetchRankList = async () => {
    setLoading(true);
    try {
      const response = await apiService.getRankList(Number(actId!), batchId!);
      if (response.code === 0 || !response.code) {
        setGroupList(response.data.groupInfos);
      } else {
        message.error(response.message || '获取赛程榜单失败');
      }
    } catch (error) {
      message.error('网络请求失败');
      console.error('获取赛程榜单错误:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleRankClick = (phaseId: number, rankId: number, rankName: string, beginTime: string, endTime: string) => {
    navigate(`/event-list/${actId}/${batchId}/${phaseId}/${rankId}`, {
      state: {
        rankName,
        phaseBeginTime: beginTime,
        phaseEndTime: endTime
      }
    });
  };

  const renderPhaseTimeline = (phases: PhaseInfo[]) => {
    if (phases.length === 0) return null;

    return (
      <div className="custom-timeline" style={{ width: '100%' }}>
        <div style={{
          display: 'flex',
          minWidth: `${phases.length * 280}px`,
          position: 'relative'
        }}>
          {phases.map((phase, index) => (
            <div key={phase.phaseId} style={{
              flex: '0 0 280px',
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              position: 'relative'
            }}>
              {/* 上方：榜单列表 */}
              <div style={{
                marginBottom: '20px',
                display: 'flex',
                flexDirection: 'column',
                gap: '6px',
                alignItems: 'center',
                minHeight: '80px',
                justifyContent: 'flex-end'
              }}>
                {phase.rankInfos.map((rank) => (
                  <Button
                    key={rank.rankId}
                    type="link"
                    size="small"
                    onClick={() => handleRankClick(phase.phaseId, rank.rankId, rank.rankName, phase.beginTime, phase.endTime)}
                    style={{
                      padding: '4px 8px',
                      height: 'auto',
                      fontSize: '12px',
                      fontWeight: '500',
                      textAlign: 'center'
                    }}
                  >
                    {rank.rankName}
                  </Button>
                ))}
              </div>

              {/* 时间轴节点 */}
              <div
                className="timeline-node"
                style={{
                  width: '14px',
                  height: '14px',
                  borderRadius: '50%',
                  backgroundColor: '#1890ff',
                  border: '3px solid #fff',
                  boxShadow: '0 0 0 2px #1890ff, 0 2px 4px rgba(0,0,0,0.1)',
                  position: 'relative',
                  zIndex: 2,
                  cursor: 'pointer'
                }}
              />

              {/* 时间轴连接线 */}
              {index < phases.length - 1 && (
                <div style={{
                  position: 'absolute',
                  top: '107px', // 榜单区域高度 + margin + 节点中心偏移
                  left: '50%',
                  width: '280px',
                  height: '3px',
                  background: 'linear-gradient(to right, #1890ff, #40a9ff)',
                  zIndex: 1,
                  transform: 'translateX(-7px)', // 节点半径偏移
                  borderRadius: '2px'
                }} />
              )}

              {/* 下方：阶段信息 */}
              <div style={{
                marginTop: '24px',
                textAlign: 'center',
                maxWidth: '240px',
                padding: '12px',
                backgroundColor: '#fafafa',
                borderRadius: '8px',
                border: '1px solid #f0f0f0'
              }}>
                <div style={{
                  fontWeight: 'bold',
                  fontSize: '14px',
                  marginBottom: '8px',
                  color: '#262626'
                }}>
                  {phase.phaseName}
                </div>
                <div style={{
                  fontSize: '12px',
                  color: '#8c8c8c',
                  lineHeight: '1.5'
                }}>
                  <div style={{ marginBottom: '2px' }}>
                    <span style={{ fontWeight: '500' }}>开始:</span> {dayjs(phase.beginTime).format('MM-DD HH:mm')}
                  </div>
                  <div>
                    <span style={{ fontWeight: '500' }}>结束:</span> {dayjs(phase.endTime).format('MM-DD HH:mm')}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  };

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '100px' }}>
        <Spin size="large" />
      </div>
    );
  }

  return (
    <div className="page-container">
      <div style={{ marginBottom: '24px', width: '100%' }}>
        <Button
          icon={<ArrowLeftOutlined />}
          onClick={() => navigate('/')}
          style={{ marginRight: '16px' }}
        >
          返回首页
        </Button>
        <span style={{ fontSize: '16px', fontWeight: 'bold' }}>
          活动ID: {actId} | 批次: {batchId}
        </span>
      </div>

      {groupList.length === 0 ? (
        <Card style={{ width: '100%' }}>
          <div style={{ textAlign: 'center', padding: '50px', color: '#999' }}>
            暂无赛程数据
          </div>
        </Card>
      ) : (
        <div style={{ display: 'flex', flexDirection: 'column', gap: '24px', width: '100%' }}>
          {groupList.map((group, index) => (
            <Card
              key={index}
              title={group.groupName}
              style={{ width: '100%' }}
            >
              {renderPhaseTimeline(group.phaseInfos)}
            </Card>
          ))}
        </div>
      )}
    </div>
  );
};

export default RankListPage;
