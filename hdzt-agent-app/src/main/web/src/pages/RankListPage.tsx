import React, { useState, useEffect } from 'react';
import { Card, message, Spin, Button, Timeline } from 'antd';
import { useParams, useNavigate } from 'react-router-dom';
import { ArrowLeftOutlined } from '@ant-design/icons';
import { apiService, GroupInfo, PhaseInfo, RankInfo } from '../services/api';
import dayjs from 'dayjs';

const RankListPage: React.FC = () => {
  const { actId, batchId } = useParams<{ actId: string; batchId: string }>();
  const [groupList, setGroupList] = useState<GroupInfo[]>([]);
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();

  useEffect(() => {
    if (actId && batchId) {
      fetchRankList();
    }
  }, [actId, batchId]);

  const fetchRankList = async () => {
    setLoading(true);
    try {
      const response = await apiService.getRankList(Number(actId!), batchId!);
      if (response.code === 0 || !response.code) {
        setGroupList(response.data.groupInfos);
      } else {
        message.error(response.message || '获取赛程榜单失败');
      }
    } catch (error) {
      message.error('网络请求失败');
      console.error('获取赛程榜单错误:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleRankClick = (phaseId: number, rankId: number, rankName: string) => {
    navigate(`/event-list/${actId}/${batchId}/${phaseId}/${rankId}`, {
      state: { rankName }
    });
  };

  const renderPhaseTimeline = (phases: PhaseInfo[]) => {
    const timelineItems = phases.map((phase) => ({
      children: (
        <div>
          <div style={{ marginBottom: '8px' }}>
            <strong>{phase.phaseName}</strong>
          </div>
          <div style={{ fontSize: '12px', color: '#666', marginBottom: '12px' }}>
            {dayjs(phase.beginTime).format('MM-DD HH:mm')} ~ {dayjs(phase.endTime).format('MM-DD HH:mm')}
          </div>
          <div style={{ display: 'flex', flexDirection: 'column', gap: '4px' }}>
            {phase.rankInfos.map((rank) => (
              <Button
                key={rank.rankId}
                type="link"
                size="small"
                onClick={() => handleRankClick(phase.phaseId, rank.rankId, rank.rankName)}
                style={{ textAlign: 'left', padding: '2px 8px', height: 'auto' }}
              >
                {rank.rankName}
              </Button>
            ))}
          </div>
        </div>
      ),
    }));

    return <Timeline items={timelineItems} />;
  };

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '100px' }}>
        <Spin size="large" />
      </div>
    );
  }

  return (
    <div style={{ padding: '24px', maxWidth: '1200px', margin: '0 auto' }}>
      <div style={{ marginBottom: '24px' }}>
        <Button 
          icon={<ArrowLeftOutlined />} 
          onClick={() => navigate('/')}
          style={{ marginRight: '16px' }}
        >
          返回首页
        </Button>
        <span style={{ fontSize: '16px', fontWeight: 'bold' }}>
          活动ID: {actId} | 批次: {batchId}
        </span>
      </div>

      {groupList.length === 0 ? (
        <Card>
          <div style={{ textAlign: 'center', padding: '50px', color: '#999' }}>
            暂无赛程数据
          </div>
        </Card>
      ) : (
        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(400px, 1fr))', gap: '24px' }}>
          {groupList.map((group, index) => (
            <Card
              key={index}
              title={group.groupName}
              style={{ height: 'fit-content' }}
            >
              {renderPhaseTimeline(group.phaseInfos)}
            </Card>
          ))}
        </div>
      )}
    </div>
  );
};

export default RankListPage;
