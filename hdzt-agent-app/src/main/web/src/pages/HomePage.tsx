import React, { useState, useEffect } from 'react';
import { Card, Input, Button, List, message, Spin } from 'antd';
import { useNavigate } from 'react-router-dom';
import { apiService, BatchVo } from '../services/api';
import dayjs from 'dayjs';

const HomePage: React.FC = () => {
  const [actId, setActId] = useState<string>('');
  const [batchList, setBatchList] = useState<BatchVo[]>([]);
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();

  useEffect(() => {
    // 页面加载时默认查询批次列表
    handleSearch();
  }, []);

  const handleSearch = async () => {
    setLoading(true);
    try {
      // 如果没有输入活动ID，传入默认值或null
      const searchActId = actId ? Number(actId) : 1001; // 使用默认活动ID 1001
      const response = await apiService.getBatchList(searchActId);
      if (response.code === 0 || !response.code) {
        setBatchList(response.data.batchVoList);
        if (response.data.batchVoList.length === 0) {
          message.info('暂无批次数据');
        }
      } else {
        message.error(response.message || '获取批次列表失败');
      }
    } catch (error) {
      message.error('网络请求失败');
      console.error('获取批次列表错误:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleBatchClick = (batch: BatchVo) => {
    navigate(`/rank-list/${batch.actId}/${batch.batchId}`);
  };

  return (
    <div className="page-container">
      <Card title="活动排行榜事件查询系统" style={{ marginBottom: '24px', width: '100%' }}>
        <div style={{ display: 'flex', gap: '16px', alignItems: 'center', flexWrap: 'wrap' }}>
          <Input
            placeholder="请输入活动ID（可选，默认1001）"
            value={actId}
            onChange={(e) => setActId(e.target.value)}
            style={{ width: '300px', minWidth: '200px' }}
            onPressEnter={handleSearch}
          />
          <Button type="primary" onClick={handleSearch} loading={loading}>
            查询批次列表
          </Button>
        </div>
      </Card>

      {loading && (
        <div style={{ textAlign: 'center', padding: '50px' }}>
          <Spin size="large" />
        </div>
      )}

      {!loading && batchList.length > 0 && (
        <Card title="批次列表" style={{ width: '100%' }}>
          <List
            grid={{ gutter: 16, column: 1 }}
            dataSource={batchList}
            renderItem={(batch) => (
              <List.Item>
                <Card
                  hoverable
                  onClick={() => handleBatchClick(batch)}
                  style={{ cursor: 'pointer', width: '100%' }}
                >
                  <div style={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    flexWrap: 'wrap',
                    gap: '16px'
                  }}>
                    <div style={{ flex: '1', minWidth: '200px' }}>
                      <h3 style={{ margin: 0, color: '#1890ff' }}>批次: {batch.batchId}</h3>
                      <p style={{ margin: '8px 0 0 0', color: '#666' }}>
                        创建者: {batch.createUid}
                      </p>
                    </div>
                    <div style={{ textAlign: 'right', minWidth: '200px' }}>
                      <p style={{ margin: 0, fontSize: '12px', color: '#999' }}>
                        开始时间: {dayjs(batch.startTime).format('YYYY-MM-DD HH:mm:ss')}
                      </p>
                      <p style={{ margin: '4px 0 0 0', fontSize: '12px', color: '#999' }}>
                        结束时间: {dayjs(batch.endTime).format('YYYY-MM-DD HH:mm:ss')}
                      </p>
                    </div>
                  </div>
                </Card>
              </List.Item>
            )}
          />
        </Card>
      )}
    </div>
  );
};

export default HomePage;
