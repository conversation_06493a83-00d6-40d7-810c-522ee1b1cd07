{"busiId": "810", "actId": "202308n001", "actName": "七夕浪漫爱情盛典活动", "beginTime": "2023-08-18 17:00:00", "endTime": "2023-08-31 23:59:59", "actRoles": [{"roleId": "81001", "remark": "主持", "autoEnroll": "1", "checkFlag": "1"}, {"roleId": "81050", "remark": "强厅", "autoEnroll": "1", "checkFlag": "1"}, {"roleId": "81070", "remark": "家族", "autoEnroll": "1", "checkFlag": "1"}, {"roleId": "80003", "remark": "YO神豪", "autoEnroll": "1", "checkFlag": "0"}, {"roleId": "81003", "remark": "贡献神豪", "autoEnroll": "1", "checkFlag": "0"}, {"roleId": "81006", "remark": "百度神豪", "autoEnroll": "1", "checkFlag": "0"}, {"roleId": "81007", "remark": "非百度神豪", "autoEnroll": "1", "checkFlag": "0"}], "actGifts": [{"busiItemId": "340371", "hdztItemId": "yin<PERSON>_pai", "itemName": "姻缘牌", "itemScore": "10", "remark": "礼物栏普通礼物"}, {"busiItemId": "340373", "hdztItemId": "qiannian_yue", "itemName": "千年约定", "itemScore": "131400", "remark": "礼物栏普通礼物"}, {"busiItemId": "340374", "hdztItemId": "yuan_ding", "itemName": "缘定是你", "itemScore": "209900", "remark": "告白墙礼物"}, {"busiItemId": "340375", "hdztItemId": "yin<PERSON>_piao", "itemName": "姻缘票", "itemScore": "5", "remark": "免费礼物，只算进榜单，不算进玩法（活动结束后1天失效）"}, {"busiItemId": "340095", "hdztItemId": "fensipiao", "itemName": "粉丝票", "itemScore": "5", "remark": "免费礼物，只算进榜单，不算进玩法（活动结束后1天失效）"}, {"busiItemId": "340372", "hdztItemId": "lian_qiaoguo", "itemName": "恋恋巧果", "itemScore": "0", "remark": "不算榜单和玩法分值"}, {"busiItemId": "340342", "hdztItemId": "shouhu_kun", "itemName": "守护之鲲", "itemScore": "888800", "remark": "海底秘境产出礼物"}], "actSchedules": [{"id": "1", "scheduleName": "主持赛程", "scheduleCode": "zhuchi_rank", "actRankPhases": [{"phaseId": "10", "phaseName": "定级赛", "beginTime": "2023-08-18 17:00:00", "endTime": "2023-08-19 23:59:59"}, {"phaseId": "11", "phaseName": "晋级赛", "beginTime": "2023-08-20 00:00:00", "endTime": "2023-08-21 23:59:59"}, {"phaseId": "12", "phaseName": "PK赛", "beginTime": "2023-08-22 00:00:00", "endTime": "2023-08-23 23:59:59"}, {"phaseId": "13", "phaseName": "决赛", "beginTime": "2023-08-24 00:00:00", "endTime": "2023-08-24 23:59:59"}], "actRankConfigs": [{"rankId": "100", "rankName": "定级赛道", "beginTime": "2023-08-18 17:00:00", "endTime": "2023-08-19 23:59:59", "timeKey": "0", "itemKey": "1", "memberKey": "1", "roles": "81001", "itemIds": "yin<PERSON>_pai,yuan_ding,s<PERSON><PERSON>_kun,yin<PERSON>_piao,fensipiao"}, {"rankId": "101", "rankName": "卓越赛道", "beginTime": "2023-08-20 00:00:00", "endTime": "2023-08-24 23:59:59", "timeKey": "0", "itemKey": "1", "memberKey": "1", "roles": "81001", "itemIds": "yin<PERSON>_pai,yuan_ding,s<PERSON><PERSON>_kun,yin<PERSON>_piao,fensipiao"}, {"rankId": "102", "rankName": "精英赛道", "beginTime": "2023-08-20 00:00:00", "endTime": "2023-08-24 23:59:59", "timeKey": "0", "itemKey": "1", "memberKey": "1", "roles": "81001", "itemIds": "yin<PERSON>_pai,yuan_ding,s<PERSON><PERSON>_kun,yin<PERSON>_piao,fensipiao"}]}, {"id": "2", "scheduleName": "强厅赛程", "scheduleCode": "qiangting_rank", "actRankPhases": [{"phaseId": "20", "phaseName": "定级赛", "beginTime": "2023-08-25 00:00:00", "endTime": "2023-08-26 23:59:59"}, {"phaseId": "21", "phaseName": "晋级赛", "beginTime": "2023-08-27 00:00:00", "endTime": "2023-08-28 23:59:59"}, {"phaseId": "22", "phaseName": "PK赛", "beginTime": "2023-08-29 00:00:00", "endTime": "2023-08-30 23:59:59"}, {"phaseId": "23", "phaseName": "决赛", "beginTime": "2023-08-31 00:00:00", "endTime": "2023-08-31 23:59:59"}], "actRankConfigs": [{"rankId": "200", "rankName": "定级赛道", "beginTime": "2023-08-25 00:00:00", "endTime": "2023-08-26 23:59:59", "timeKey": "0", "itemKey": "1", "memberKey": "1", "roles": "81050", "itemIds": "yin<PERSON>_pai,yuan_ding,s<PERSON><PERSON>_kun,yin<PERSON>_piao,fensipiao"}, {"rankId": "201", "rankName": "卓越赛道", "beginTime": "2023-08-27 00:00:00", "endTime": "2023-08-31 23:59:59", "timeKey": "0", "itemKey": "1", "memberKey": "1", "roles": "81050", "itemIds": "yin<PERSON>_pai,yuan_ding,s<PERSON><PERSON>_kun,yin<PERSON>_piao,fensipiao"}, {"rankId": "202", "rankName": "精英赛道", "beginTime": "2023-08-27 00:00:00", "endTime": "2023-08-31 23:59:59", "timeKey": "0", "itemKey": "1", "memberKey": "1", "roles": "81050", "itemIds": "yin<PERSON>_pai,yuan_ding,s<PERSON><PERSON>_kun,yin<PERSON>_piao,fensipiao"}, {"rankId": "203", "rankName": "星锐赛道", "beginTime": "2023-08-27 00:00:00", "endTime": "2023-08-31 23:59:59", "timeKey": "0", "itemKey": "1", "memberKey": "1", "roles": "81050", "itemIds": "yin<PERSON>_pai,yuan_ding,s<PERSON><PERSON>_kun,yin<PERSON>_piao,fensipiao"}]}, {"id": "3", "scheduleName": "家族总榜", "scheduleCode": "jiazu_rank", "actRankPhases": [{"phaseId": "30", "phaseName": "活动全程", "beginTime": "2023-08-18 17:00:00", "endTime": "2023-08-31 23:59:59"}], "actRankConfigs": [{"rankId": "300", "rankName": "家族总榜", "beginTime": "2023-08-18 17:00:00", "endTime": "2023-08-31 23:59:59", "timeKey": "0", "itemKey": "1", "memberKey": "1", "roles": "81070", "itemIds": "yin<PERSON>_pai,yuan_ding,s<PERSON><PERSON>_kun,yin<PERSON>_piao,fensipiao"}]}, {"id": "4", "scheduleName": "神豪榜", "scheduleCode": "shenhao_rank", "actRankPhases": [{"phaseId": "40", "phaseName": "活动全程", "beginTime": "2023-08-18 17:00:00", "endTime": "2023-08-31 23:59:59"}], "actRankConfigs": [{"rankId": "400", "rankName": "贡献神豪", "beginTime": "2023-08-18 17:00:00", "endTime": "2023-08-31 23:59:59", "timeKey": "0", "itemKey": "1", "memberKey": "1", "roles": "81003", "itemIds": "y<PERSON><PERSON>_pai,yuan_ding,shou<PERSON>_kun"}, {"rankId": "401", "rankName": "YO神豪", "beginTime": "2023-08-18 17:00:00", "endTime": "2023-08-31 23:59:59", "timeKey": "0", "itemKey": "1", "memberKey": "1", "roles": "80003", "itemIds": "y<PERSON><PERSON>_pai,yuan_ding,shou<PERSON>_kun"}, {"rankId": "402", "rankName": "百度神豪", "beginTime": "2023-08-18 17:00:00", "endTime": "2023-08-31 23:59:59", "timeKey": "0", "itemKey": "1", "memberKey": "1", "roles": "81006", "itemIds": "y<PERSON><PERSON>_pai,yuan_ding,shou<PERSON>_kun"}, {"rankId": "403", "rankName": "幸运神豪", "beginTime": "2023-08-18 17:00:00", "endTime": "2023-08-31 23:59:59", "timeKey": "0", "itemKey": "1", "memberKey": "1", "roles": "81007", "itemIds": "y<PERSON><PERSON>_pai,yuan_ding,s<PERSON><PERSON>_kun,lian_qia<PERSON>uo"}]}]}