### act
# @timeout = 300000
POST http://localhost:8786/api/act/chat
Content-Type: application/json
Connection: keep-alive
Accept: application/json

{
  "document": "---聊天室8月七夕浪漫爱情盛典活动_左嘉炜20250708------修订历史------|文档版本|修订日期|修订说明（C:创建 A:追加 M:更改 D:删除）||修订者|---|-|-|-|-|-|---|V1.0|2025/07/08|C|创建|左左|---|V1.1|2025/07/10|M|心动爱雨：调整爱心雨的下落和抽奖过程||------------### 1.需求概述---#### 1.1活动背景---活动时间：8月18日17:00-8月31日24:00（14天）------目标增量：1000W------活动名：七夕浪漫爱情盛典活动------活动上线端：PCYY、YO语音------模板：聊天室、SDK---活动礼物：------|业务|礼物ID|礼物名|价值|分值|备注|---|-|-|-|-|-|-|---|聊天室|||0.1Y|10荣耀值/贡献值|礼物栏普通礼物|---|聊天室|||1314Y|131400荣耀值/贡献值|礼物栏普通礼物|---|聊天室|||2099Y|209900荣耀值/贡献值|告白墙礼物|---|聊天室|||/|5荣耀值/贡献值|免费礼物，只算进榜单，不算进玩法（活动结束后1天失效）|---|聊天室|340095|粉丝票|/|5荣耀值/贡献值|免费礼物，只算进榜单，不算进玩法（活动结束后1天失效）|---|聊天室|340342|守护之鲲|8888Y|888800荣耀值/贡献值|海底秘境产出礼物|---------### 2.赛制说明---|8月|18|19|20|21|22|23|24|25|26|27|28|29|30|31|---|-|-|-|-|-|-|-|-|-|-|-|-|-|-|-|---|星期|一|二|三|四|五|六|日|一|二|三|四|五|六|日|---|主持|定级赛---（N进200）||卓越晋级赛---（80进40）||卓越PK赛---（40进22，败方前2复活）||卓越决赛（22进8，决赛拍卖激励）|/|||||||---||||精英晋级赛---（120进60）||精英PK赛---（60进31，败方前1复活）||精英决赛---（31进5，决赛拍卖激励）||||||||---|强厅|/|||||||定级赛（N进170）||卓越晋级赛（30进20）||卓越PK赛（20进13，败方前3复活）||卓越决赛（13进5，决赛拍卖激励）|---|||||||||||精英晋级赛（50进30）||精英PK赛（30进17，败方前2复活）||精英决赛（17进3，决赛拍卖激励）|---|||||||||||星锐晋级赛（90进50）||星锐PK赛（50进26，败方前1复活）||星锐决赛（26进3，决赛拍卖激励）|---|家族|总榜||||||||||||||---|神豪|贡献神豪、Yo神豪、幸运神豪、百度神豪||||||||||||||---------#### 2.1主持赛程---1、定级赛------时间：8月18日-8月19日------对象：聊天室签约主持（按房间ID加入黑名单，主持、神豪在黑名单房间不累计榜单和玩法数值）------规则：按荣耀值进行排名，排名前80名为卓越主持，81-200名为精英主持------榜单显示：显示前200名------------2、晋级赛------时间：8月20日-8月21日------对象：上一轮晋级的主持------规则：按荣耀值进行排名，卓越赛道80进40，精英赛道120进60------榜单显示：显示前200名------------3、PK赛------时间：8月22日-8月23日------对象：上一轮晋级的主持------规则：------（1）各赛道主持按1V3，2V4，5V7，6V8...以此类推的方式进行PK------（2）PK获胜者晋级，卓越赛道败方分数最高的前2名复活，精英赛道败方分数最高的第1名复活------------4、决赛------时间：8月24日------对象：上一轮晋级的主持------规则：累计荣耀值排名，荣耀值相同，先到达者靠前，本轮将决出卓越主持8强、精英主持5强------------#### 2.2强厅赛程---##### 2.2.1参与对象---（1）Yo语音及SDK房间：仅经营厅参与，Yo语音强厅取房间号（部分强厅要加入黑名单，不参与赛制，也不参与个人玩法）------------##### 2.2.2卓越、精英、星锐强厅赛程说明---1、定级赛------时间：8月25日-8月26日------对象：所有参赛强厅（要求主持签约的家族ID和当前收礼的房间对应的家族ID需一致，不一致时不累计强厅榜单分数。）------规则：按荣耀值进行排名，排名前30名为卓越强厅，31-80名为精英强厅，81名-170名为星锐强厅------榜单显示：显示前200名------------2、晋级赛------时间：8月27日-8月28日------对象：上一轮晋级的各赛道强厅------规则：按荣耀值进行排名，卓越赛道30进20，精英赛50进30，星锐赛道90进50------榜单显示：显示前200名------------3、PK赛------时间：8月29日-8月30日------对象：上一轮晋级的各赛道强厅------规则：------（1）各赛道强厅按1V3，2V4，5V7，6V8...以此类推的方式进行PK---（2）PK获胜者晋级，卓越赛道败方分数最高的前3名复活，精英赛道败方分数最高的前2名复活，星锐赛道败方分数最高的第1名复活------------4、决赛------时间：8月31日------对象：上一轮晋级的各赛道强厅------规则：累计荣耀值排名，荣耀值相同，先到达者靠前，本轮将决出卓越强厅5强、精英强厅3强、星锐强厅3强------------#### 2.3家族总榜---参与对象：以家族ID为主体的语音房家族------时间：8月18日-8月31日------规则：按家族旗下签约主持产生荣耀值之和进行排名------**说明：主持若在活动中途改签，则改签后收集礼物累计到新家族中**------#### 2.4神豪榜---##### 2.4.1贡献神豪---时间：8月18日-8月31日------对象：语音房送礼的用户（SDK端送礼不计入贡献总榜）------规则：统计用户在活动期间送出活动礼物的总分值，0.1Y币=10贡献值------------##### 2.4.2YO神豪---时间：8月18日-8月31日------对象：Yo语音移动端送礼的用户------规则：用户在Yo语音移动端房间送出活动礼物的总分值，0.1Y币=10贡献值------------##### 2.4.3幸运神豪---时间：8月18日-8月31日------对象：语音房、SDK参与概率玩法用户------规则：用户在Yo语音、SDK的抢物资、幸运小狗、漂流瓶、海底秘境、抢物资、丘比特乐园玩法抽中礼物价值，0.1Y币礼物价值=10幸运值（含活动礼物和非活动礼物）------------##### 2.4.4百度神豪---时间：8月18日-8月31日------对象：SDK房间送礼的用户------规则：仅统计用户在贴吧、好看端活动期间送出活动礼物的总分值，0.1Y币=10贡献值---",
  "model": "DashScope",
  "sessionId": "lwz-act-test-41"
}


### req
POST http://localhost:8786/api/test/req
Content-Type: application/json

{
  "data": {
    "audit": {
      "req": 1
    }
  }
}