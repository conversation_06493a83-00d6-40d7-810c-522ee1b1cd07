package com.yy.hdzt;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.ai.tool.annotation.Tool;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.client.RestClientException;

@Service
public class JsonComponentService {
    private static final Logger log = LoggerFactory.getLogger(JsonComponentService.class);

    private final RestTemplate restTemplate;
    private static final String INIT_ACT_COMPONENT_URL = "http://localhost:8787/api/act/initActComponent/body";

    @Autowired
    public JsonComponentService(RestTemplate restTemplate) {
        this.restTemplate = restTemplate;
    }

    @Tool(name = "genJsonComponent", description = "处理组件JSON配置并初始化活动组件 需要json字符串")
    public String processJsonComponent(String json) {
        log.info("开始处理组件JSON配置，JSON长度: {}", json != null ? json.length() : 0);

        try {
            // 参数验证
            if (json == null || json.trim().isEmpty()) {
                return "错误: JSON内容不能为空";
            }

            // 调用initActComponent接口
            String result = callInitActComponentApi(json);

            log.info("组件JSON配置处理完成");
            return result;

        } catch (Exception e) {
            log.error("处理组件JSON配置失败: {}", e.getMessage(), e);
            return "处理失败: " + e.getMessage();
        }
    }

    /**
     * 调用initActComponent API
     *
     * @param json JSON内容
     * @return API响应结果
     */
    private String callInitActComponentApi(String json) {
        try {
            log.info("调用initActComponent API: {}", INIT_ACT_COMPONENT_URL);

            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            // 创建请求实体
            HttpEntity<String> requestEntity = new HttpEntity<>(json, headers);

            // 发送POST请求
            ResponseEntity<String> response = restTemplate.exchange(
                INIT_ACT_COMPONENT_URL,
                HttpMethod.POST,
                requestEntity,
                String.class
            );

            // 检查响应状态
            if (response.getStatusCode().is2xxSuccessful()) {
                String responseBody = response.getBody();
                log.info("API调用成功，响应: {}", responseBody);
                return "成功: " + responseBody;
            } else {
                log.warn("API调用返回非成功状态码: {}", response.getStatusCode());
                return "API调用失败，状态码: " + response.getStatusCode();
            }

        } catch (RestClientException e) {
            log.error("调用initActComponent API失败: {}", e.getMessage(), e);
            return "API调用异常: " + e.getMessage();
        }
    }

    /**
     * 获取组件JSON示例
     *
     * @return JSON示例
     */
    @Tool(name = "getComponentJsonExample", description = "获取组件JSON配置示例")
    public String getComponentJsonExample() {
        return """
            {
              "2025073001|1016|810": {
                "actId": 2025073001,
                "cmptId": 1016,
                "cmptUseInx": 810,
                "busiId": 400,
                "giftIds": ["20001", "20002"],
                "apps": ["yy"],
                "giftIcons": {
                  "20001": "gift_icon_20001",
                  "20002": "gift_icon_20002"
                },
                "tAwardPkgInfos": {
                  "2001": {
                    "medalPic": "medal_primary_pic_2",
                    "medalName": "新手勋章",
                    "qrCode": "qr_code_primary_2"
                  },
                  "2002": {
                    "medalPic": "medal_senior_pic_2",
                    "medalName": "进阶勋章",
                    "qrCode": "qr_code_senior_2"
                  }
                },
                "tAwardTskMedalId": 2002,
                "tAwardPkgPrimaryId": 2001,
                "tAwardPkgSeniorId": 2002,
                "tAwardPkgExpertId": 0,
                "expertRankId": 0,
                "expertPhaseId": 0,
                "threshold": 50000
              }
            }
            """;
    }
}
